"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/vps/configure/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx":
/*!***********************************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfigureVPSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/vpsService */ \"(app-pages-browser)/./src/app/services/vpsService.js\");\n/* harmony import */ var _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/cartService */ \"(app-pages-browser)/./src/app/services/cartService.js\");\n/* harmony import */ var _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/packageService */ \"(app-pages-browser)/./src/app/services/packageService.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/context/AuthContext */ \"(app-pages-browser)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Modern OS Icons Components\nconst UbuntuIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n};\n_c = UbuntuIcon;\nconst CentOSIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = CentOSIcon;\nconst DebianIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = DebianIcon;\nconst WindowsIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 76,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = WindowsIcon;\nfunction ConfigureVPSPage() {\n    var _operatingSystems_find, _locations_find;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"vps_configure\");\n    const { setCartCount } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // State management\n    const [vpsPlans, setVpsPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [osImages, setOsImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [regions, setRegions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderLoading, setOrderLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize plan from URL params\n    const planId = searchParams.get(\"plan\");\n    const autoBackup = searchParams.get(\"autobackup\") === \"true\";\n    const [selectedPlan, setSelectedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAutoBackup, setIsAutoBackup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    // Function to parse specifications from database\n    const parseSpecifications = (specifications, description)=>{\n        let cores = 0, ram = \"0 GB\", storage = \"0 GB\", traffic = \"32 TB\";\n        // Parse from specifications array\n        if (specifications && Array.isArray(specifications)) {\n            specifications.forEach((spec)=>{\n                const value = spec.value || \"\";\n                const lowerValue = value.toLowerCase();\n                // Parse CPU cores\n                if (lowerValue.includes(\"cpu\") || lowerValue.includes(\"core\") || lowerValue.includes(\"vcpu\")) {\n                    const cpuMatch = value.match(/(\\d+)/);\n                    if (cpuMatch) cores = parseInt(cpuMatch[1]);\n                }\n                // Parse RAM\n                if (lowerValue.includes(\"ram\") || lowerValue.includes(\"memory\") || lowerValue.includes(\"gb ram\")) {\n                    const ramMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n                }\n                // Parse Storage\n                if (lowerValue.includes(\"storage\") || lowerValue.includes(\"disk\") || lowerValue.includes(\"ssd\") || lowerValue.includes(\"nvme\")) {\n                    const storageMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (storageMatch) {\n                        const storageType = lowerValue.includes(\"nvme\") ? \"NVMe\" : lowerValue.includes(\"ssd\") ? \"SSD\" : \"\";\n                        storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n                    }\n                }\n                // Parse Traffic/Bandwidth\n                if (lowerValue.includes(\"traffic\") || lowerValue.includes(\"bandwidth\") || lowerValue.includes(\"transfer\")) {\n                    const trafficMatch = value.match(/(\\d+)\\s*(tb|gb)/i);\n                    if (trafficMatch) {\n                        traffic = \"\".concat(trafficMatch[1], \" \").concat(trafficMatch[2].toUpperCase());\n                    }\n                }\n            });\n        }\n        // Fallback: parse from description if specifications are empty\n        if (cores === 0 && description) {\n            const descLower = description.toLowerCase();\n            const cpuMatch = description.match(/(\\d+)\\s*(cpu|core|vcpu)/i);\n            if (cpuMatch) cores = parseInt(cpuMatch[1]);\n            const ramMatch = description.match(/(\\d+)\\s*gb\\s*ram/i);\n            if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n            const storageMatch = description.match(/(\\d+)\\s*gb\\s*(storage|disk|ssd|nvme)/i);\n            if (storageMatch) {\n                const storageType = descLower.includes(\"nvme\") ? \"NVMe\" : descLower.includes(\"ssd\") ? \"SSD\" : \"\";\n                storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n            }\n        }\n        return {\n            cores,\n            ram,\n            storage,\n            traffic\n        };\n    };\n    // Fetch VPS packages and find the selected one\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchVPSPackages = async ()=>{\n            try {\n                setLoading(true);\n                // Récupérer les packages VPS depuis la base de données\n                const response = await _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPackages(\"VPS Hosting\");\n                console.log(\"VPS packages response:\", response);\n                let vpsPackages = [];\n                if (response.data && Array.isArray(response.data)) {\n                    vpsPackages = response.data;\n                } else if (response.data && response.data.packages && Array.isArray(response.data.packages)) {\n                    vpsPackages = response.data.packages;\n                } else if (Array.isArray(response)) {\n                    vpsPackages = response;\n                }\n                // Transformer les packages de la base de données\n                const transformedPlans = vpsPackages.map((pkg)=>{\n                    const specs = parseSpecifications(pkg.specifications, pkg.description);\n                    return {\n                        id: pkg._id,\n                        _id: pkg._id,\n                        name: pkg.name,\n                        price: pkg.price,\n                        cores: specs.cores,\n                        ram: specs.ram,\n                        storage: specs.storage,\n                        traffic: specs.traffic,\n                        description: pkg.description,\n                        specifications: pkg.specifications\n                    };\n                });\n                setVpsPlans(transformedPlans);\n                // Trouver le package sélectionné par son ID\n                if (planId && transformedPlans.length > 0) {\n                    const foundPlan = transformedPlans.find((plan)=>{\n                        var _plan__id, _plan_id;\n                        return plan._id === planId || plan.id === planId || ((_plan__id = plan._id) === null || _plan__id === void 0 ? void 0 : _plan__id.toString()) === planId || ((_plan_id = plan.id) === null || _plan_id === void 0 ? void 0 : _plan_id.toString()) === planId;\n                    });\n                    if (foundPlan) {\n                        setSelectedPlan(foundPlan);\n                        console.log(\"Selected plan found:\", foundPlan);\n                    } else {\n                        console.error(\"Plan not found with ID:\", planId);\n                        console.log(\"Available plans:\", transformedPlans.map((p)=>({\n                                id: p.id,\n                                _id: p._id,\n                                name: p.name\n                            })));\n                        setError(\"Package VPS non trouv\\xe9\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error fetching VPS plans:\", error);\n                setError(\"Erreur lors du chargement des plans VPS\");\n                setVpsPlans([]);\n                setSelectedPlan(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        // Fetch dynamic OS images from API\n        const fetchOSImages = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching OS images from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                console.log(\"✅ OS Images response:\", response);\n                let images = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    images = response.data.data;\n                } else if (response.data && response.data.images && Array.isArray(response.data.images)) {\n                    images = response.data.images;\n                }\n                // Transform API data to expected format\n                const transformedImages = images.map((img)=>({\n                        id: img.imageId || img.id,\n                        name: img.name,\n                        description: img.description,\n                        type: img.osType || \"linux\",\n                        version: img.version,\n                        provider: img.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting OS images:\", transformedImages.length, \"images\");\n                setOsImages(transformedImages);\n                // Set default OS if available\n                if (transformedImages.length > 0) {\n                    const defaultOS = transformedImages.find((img)=>img.name.toLowerCase().includes(\"ubuntu\") && img.name.toLowerCase().includes(\"22.04\")) || transformedImages[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default OS:\", defaultOS.name, defaultOS.id);\n                    setSelectedOS(defaultOS.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching OS images:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticOsImages = [\n                    {\n                        id: \"ubuntu-20.04\",\n                        name: \"Ubuntu 20.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"ubuntu-22.04\",\n                        name: \"Ubuntu 22.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"centos-7\",\n                        name: \"CentOS 7\",\n                        type: \"centos\"\n                    },\n                    {\n                        id: \"debian-11\",\n                        name: \"Debian 11\",\n                        type: \"debian\"\n                    }\n                ];\n                setOsImages(staticOsImages);\n                setSelectedOS(\"ubuntu-22.04\");\n            }\n        };\n        // Fetch dynamic regions from API\n        const fetchRegions = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching regions from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                console.log(\"✅ Regions response:\", response);\n                let regions = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    regions = response.data.data;\n                } else if (response.data && response.data.regions && Array.isArray(response.data.regions)) {\n                    regions = response.data.regions;\n                }\n                // Transform API data to expected format\n                const transformedRegions = regions.map((region)=>({\n                        id: region.regionSlug,\n                        name: region.regionName,\n                        provider: region.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting regions:\", transformedRegions.length, \"regions\");\n                setRegions(transformedRegions);\n                // Set default region if available\n                if (transformedRegions.length > 0) {\n                    const defaultRegion = transformedRegions.find((region)=>region.id === \"EU\") || transformedRegions[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default region:\", defaultRegion.name, defaultRegion.id);\n                    setSelectedLocation(defaultRegion.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching regions:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticRegions = [\n                    {\n                        id: \"EU\",\n                        name: \"European Union\",\n                        description: \"Germany\",\n                        country: \"Germany\",\n                        city: \"Nuremberg\"\n                    },\n                    {\n                        id: \"US-central\",\n                        name: \"United States Central\",\n                        description: \"St. Louis\",\n                        country: \"United States\",\n                        city: \"St. Louis\"\n                    },\n                    {\n                        id: \"SG\",\n                        name: \"Asia Pacific\",\n                        description: \"Singapore\",\n                        country: \"Singapore\",\n                        city: \"Singapore\"\n                    }\n                ];\n                setRegions(staticRegions);\n                setSelectedLocation(\"EU\");\n            }\n        };\n        // Appeler les fonctions pour récupérer les données\n        fetchVPSPackages();\n        fetchOSImages();\n        fetchRegions();\n    }, [\n        planId\n    ]);\n    // Configuration state\n    const [selectedOS, setSelectedOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ubuntu-20.04\");\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"france\");\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [additionalIPs, setAdditionalIPs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [backupEnabled, setBackupEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // New Contabo-style options\n    const [autoBackupOption, setAutoBackupOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [privateNetworking, setPrivateNetworking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ipv4Addresses, setIpv4Addresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [objectStorage, setObjectStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [serverManagement, setServerManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"unmanaged\");\n    const [monitoring, setMonitoring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ssl, setSsl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    // Handle adding VPS to cart\n    const handleAddToCart = async ()=>{\n        if (!selectedPlan) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez s\\xe9lectionner un plan VPS\");\n            return;\n        }\n        try {\n            var _selectedPlan_vpsConfig, _response_data_cart, _response_data;\n            setOrderLoading(true);\n            // Vérifier si nous avons un ID valide\n            const packageId = selectedPlan._id || selectedPlan.id;\n            if (!packageId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"ID du package VPS manquant. Veuillez r\\xe9essayer.\");\n                return;\n            }\n            // Map frontend selections to Contabo API format\n            const contaboRegionMap = {\n                france: \"EU\",\n                EU: \"EU\",\n                germany: \"EU\",\n                \"US-central\": \"US-east\",\n                usa: \"US-east\",\n                SG: \"SIN\",\n                singapore: \"SIN\",\n                asia: \"SIN\"\n            };\n            const contaboOSMap = {\n                \"ubuntu-20.04\": \"ubuntu-20.04\",\n                \"ubuntu-22.04\": \"ubuntu-22.04\",\n                \"ubuntu-24.04\": \"ubuntu-24.04\",\n                \"centos-7\": \"centos-7\",\n                \"centos-8\": \"centos-8\",\n                \"debian-10\": \"debian-10\",\n                \"debian-11\": \"debian-11\",\n                \"windows-2019\": \"windows-server-2019\",\n                \"windows-2022\": \"windows-server-2022\"\n            };\n            // Generate display name if not provided\n            const displayName = \"\".concat(selectedPlan.name, \"-\").concat(Date.now());\n            // Préparer les données pour l'ajout au panier avec configuration Contabo\n            const cartData = {\n                packageId: packageId,\n                quantity: quantity,\n                period: selectedPeriod === \"monthly\" ? 1 : selectedPeriod === \"6months\" ? 6 : 12,\n                // Configuration personnalisée pour Contabo VPS\n                customConfiguration: {\n                    // Contabo API fields\n                    planId: ((_selectedPlan_vpsConfig = selectedPlan.vpsConfig) === null || _selectedPlan_vpsConfig === void 0 ? void 0 : _selectedPlan_vpsConfig.providerProductId) || selectedPlan.id,\n                    provider: \"contabo\",\n                    region: contaboRegionMap[selectedLocation] || \"EU\",\n                    operatingSystem: contaboOSMap[selectedOS] || selectedOS,\n                    displayName: displayName,\n                    sshKeys: [],\n                    userData: \"\",\n                    addons: {\n                        privatenetworking: privateNetworking !== \"none\",\n                        autobackup: autoBackupOption !== \"none\",\n                        monitoring: monitoring !== \"none\"\n                    },\n                    // Plan specifications for reference\n                    cpu: selectedPlan.cores || selectedPlan.cpu,\n                    ram: selectedPlan.ram,\n                    storage: selectedPlan.storage,\n                    bandwidth: selectedPlan.traffic || selectedPlan.bandwidth,\n                    // Frontend-specific fields for display\n                    frontendConfig: {\n                        operatingSystem: selectedOS,\n                        location: selectedLocation,\n                        additionalIPs: additionalIPs,\n                        backup: isAutoBackup || backupEnabled,\n                        planName: selectedPlan.name,\n                        autoBackupOption: autoBackupOption,\n                        privateNetworking: privateNetworking,\n                        ipv4Addresses: ipv4Addresses,\n                        objectStorage: objectStorage,\n                        serverManagement: serverManagement,\n                        monitoring: monitoring,\n                        ssl: ssl\n                    }\n                }\n            };\n            console.log(\"Adding VPS to cart:\", cartData);\n            console.log(\"Selected plan:\", selectedPlan);\n            // Ajouter au panier via le service\n            const response = await _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addItemToCart(cartData);\n            console.log(\"Cart response:\", response);\n            // Mettre à jour le compteur du panier\n            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_cart = _response_data.cart) === null || _response_data_cart === void 0 ? void 0 : _response_data_cart.cartCount) {\n                setCartCount(response.data.cart.cartCount);\n            }\n            // Afficher le message de succès\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(selectedPlan.name, \" ajout\\xe9 au panier avec succ\\xe8s!\"));\n            // Rediriger vers le panier\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response2, _error_response3, _error_response4;\n            console.error(\"Error adding VPS to cart:\", error);\n            console.error(\"Error response:\", error.response);\n            console.error(\"Error data:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // Gestion des erreurs spécifiques\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.response.data.message);\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Package VPS non trouv\\xe9. Veuillez contacter le support.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez vous connecter pour ajouter au panier.\");\n                router.push(\"/auth/login\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Donn\\xe9es invalides. Veuillez v\\xe9rifier votre s\\xe9lection.\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Erreur lors de l'ajout au panier. Veuillez r\\xe9essayer.\");\n            }\n        } finally{\n            setOrderLoading(false);\n        }\n    };\n    // Use dynamic OS images data with fallback to static data\n    const operatingSystems = osImages.length > 0 ? osImages.map((os)=>({\n            id: os.id,\n            name: os.name,\n            icon: getOSIcon(os.type || os.osType),\n            type: os.type || os.osType || \"linux\",\n            description: os.description,\n            version: os.version,\n            provider: os.provider\n        })) : [\n        {\n            id: \"ubuntu-20.04\",\n            name: \"Ubuntu 20.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"ubuntu-22.04\",\n            name: \"Ubuntu 22.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"centos-8\",\n            name: \"CentOS 8\",\n            icon: CentOSIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"debian-11\",\n            name: \"Debian 11\",\n            icon: DebianIcon,\n            type: \"linux\"\n        }\n    ];\n    // Helper function to get OS icon based on type\n    function getOSIcon(osType) {\n        const iconMap = {\n            ubuntu: UbuntuIcon,\n            centos: CentOSIcon,\n            debian: DebianIcon,\n            windows: WindowsIcon,\n            linux: UbuntuIcon\n        };\n        // Check if osType contains specific OS names\n        if (osType && typeof osType === \"string\") {\n            const lowerType = osType.toLowerCase();\n            if (lowerType.includes(\"ubuntu\")) return UbuntuIcon;\n            if (lowerType.includes(\"centos\")) return CentOSIcon;\n            if (lowerType.includes(\"debian\")) return DebianIcon;\n            if (lowerType.includes(\"windows\")) return WindowsIcon;\n        }\n        return iconMap[osType] || UbuntuIcon;\n    }\n    // Use dynamic regions data with fallback to static data\n    const locations = regions.length > 0 ? regions.map((region)=>({\n            id: region.id,\n            name: region.name,\n            flag: getRegionFlag(region.country),\n            ping: getRegionPing(region.id),\n            description: region.description,\n            city: region.city,\n            country: region.country\n        })) : [\n        {\n            id: \"EU\",\n            name: \"European Union\",\n            flag: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            ping: \"15ms\",\n            description: \"Germany\",\n            city: \"Nuremberg\",\n            country: \"Germany\"\n        },\n        {\n            id: \"US-central\",\n            name: \"United States Central\",\n            flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            ping: \"120ms\",\n            description: \"St. Louis\",\n            city: \"St. Louis\",\n            country: \"United States\"\n        },\n        {\n            id: \"SG\",\n            name: \"Asia Pacific\",\n            flag: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            ping: \"200ms\",\n            description: \"Singapore\",\n            city: \"Singapore\",\n            country: \"Singapore\"\n        }\n    ];\n    // Helper functions for region display\n    function getRegionFlag(country) {\n        const flagMap = {\n            Germany: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            \"United States\": \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            Singapore: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            France: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            Netherlands: \"\\uD83C\\uDDF3\\uD83C\\uDDF1\"\n        };\n        return flagMap[country] || \"\\uD83C\\uDF0D\";\n    }\n    function getRegionPing(regionId) {\n        const pingMap = {\n            EU: \"15ms\",\n            \"US-central\": \"120ms\",\n            \"US-east\": \"110ms\",\n            \"US-west\": \"130ms\",\n            SG: \"200ms\"\n        };\n        return pingMap[regionId] || \"50ms\";\n    }\n    const calculateTotal = ()=>{\n        if (!selectedPlan) return 0;\n        let total = selectedPlan.price;\n        // Additional IPs cost\n        total += additionalIPs * 15; // 15 MAD per additional IP\n        // Backup cost (only if not auto backup plan)\n        if (!isAutoBackup && backupEnabled) {\n            total += 20; // 20 MAD for backup\n        }\n        // Auto Backup cost (Contabo style)\n        if (autoBackupOption === \"auto\") {\n            total += 18; // €1.79 ≈ 18 MAD per month\n        }\n        // Private Networking cost\n        if (privateNetworking === \"enabled\") {\n            total += 28; // 28 MAD per month\n        }\n        // IPv4 additional addresses cost\n        if (ipv4Addresses === 2) {\n            total += 42; // 42 MAD per additional IP\n        }\n        // Object Storage cost\n        const objectStorageCosts = {\n            \"250gb\": 50,\n            \"500gb\": 95,\n            \"750gb\": 140,\n            \"1tb\": 180\n        };\n        if (objectStorage !== \"none\" && objectStorageCosts[objectStorage]) {\n            total += objectStorageCosts[objectStorage];\n        }\n        // Server Management cost\n        if (serverManagement === \"managed\") {\n            total += 1340; // 1340 MAD per month for managed\n        }\n        // Monitoring cost\n        if (monitoring === \"full\") {\n            total += 140; // 140 MAD per month for full monitoring\n        }\n        // SSL cost (one-time charges, but we'll add monthly equivalent)\n        const sslCosts = {\n            basic: 76,\n            wildcard: 228\n        };\n        if (ssl !== \"none\" && sslCosts[ssl]) {\n            total += sslCosts[ssl];\n        }\n        // Apply quantity\n        total *= quantity;\n        // Period multiplier\n        const multipliers = {\n            monthly: 1,\n            \"6months\": 6 * 0.97,\n            annually: 12 * 0.9\n        };\n        return total * multipliers[selectedPeriod];\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 767,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600\",\n                        children: \"Loading VPS configuration...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 768,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 766,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 765,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-red-600 mb-2\",\n                        children: \"Error loading VPS configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 781,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 784,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        color: \"blue\",\n                        size: \"sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 785,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 780,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 779,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 sm:gap-4 w-full sm:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outlined\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.history.back(),\n                                        className: \"border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 810,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: t(\"back\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 811,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h4\",\n                                                className: \"text-lg sm:text-2xl text-gray-900 font-bold truncate\",\n                                                children: t(\"page_title\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 truncate\",\n                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 803,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left sm:text-right w-full sm:w-auto flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                        children: t(\"price_from\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 826,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        variant: \"h3\",\n                                        className: \"text-lg sm:text-2xl text-blue-600 font-bold\",\n                                        children: [\n                                            (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0,\n                                            \" MAD\",\n                                            t(\"per_month\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 825,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 802,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 801,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 800,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6 sm:space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 sm:w-6 h-5 sm:h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                variant: \"h5\",\n                                                                className: \"text-lg sm:text-xl text-gray-900 font-bold truncate\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isAutoBackup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                        children: \"Auto Backup Inclus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"vcpu_cores\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 872,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.cores) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"ram\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.ram) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 879,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"storage\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.storage) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"traffic\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.traffic) || \"0 TB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 869,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 845,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"1. \",\n                                                    t(\"billing_period\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"billing_period_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        id: \"monthly\",\n                                                        label: \"1 mois\",\n                                                        discount: \"\",\n                                                        price: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0\n                                                    },\n                                                    {\n                                                        id: \"6months\",\n                                                        label: \"6 mois\",\n                                                        discount: \"3% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 6 * 0.97)\n                                                    },\n                                                    {\n                                                        id: \"annually\",\n                                                        label: \"12 mois\",\n                                                        discount: \"10% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 12 * 0.9)\n                                                    }\n                                                ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedPeriod(period.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 rounded-full border-2 flex-shrink-0 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-600\" : \"border-gray-300\"),\n                                                                            children: selectedPeriod === period.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 962,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                                    children: period.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                period.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                                    children: period.discount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 970,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                            children: [\n                                                                                period.price,\n                                                                                \" MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 977,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: period.id === \"monthly\" ? \"/mois\" : period.id === \"6months\" ? \"/6 mois\" : \"/an\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 980,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, period.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"2. \",\n                                                    t(\"choose_os\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 998,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_os_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 17\n                                            }, this),\n                                            osImages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des syst\\xe8mes d'exploitation...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            operatingSystems.length,\n                                                            \" OS disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1012,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching OS images...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                                                                console.log(\"✅ Force fetch result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des OS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1008,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        osImages.length,\n                                                        \" syst\\xe8mes d'exploitation charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 1034,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: operatingSystems.map((os)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedOS(os.id),\n                                                        className: \"relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedOS === os.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 sm:gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(os.icon, {\n                                                                    className: \"w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-sm sm:text-base text-gray-900 break-words\",\n                                                                            children: os.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1054,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                                                            children: os.type === \"linux\" ? \"Linux Distribution\" : \"Windows Server\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1057,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1053,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedOS === os.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1064,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, os.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"3. \",\n                                                    t(\"choose_location\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_location_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 17\n                                            }, this),\n                                            regions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des emplacements...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            locations.length,\n                                                            \" emplacements disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1090,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching regions...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                                                                console.log(\"✅ Force fetch regions result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch regions error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-green-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des r\\xe9gions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1086,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        regions.length,\n                                                        \" emplacements charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: locations.map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedLocation(location.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedLocation === location.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl sm:text-2xl flex-shrink-0\",\n                                                                            children: location.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1133,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium text-sm sm:text-base text-gray-900 truncate\",\n                                                                                children: location.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1137,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1136,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1132,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedLocation === location.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 sm:w-5 h-4 sm:h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1143,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 1131,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, location.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1122,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1075,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"4. Data Protection with Auto Backup\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"auto\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"auto\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"auto\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1179,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1174,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1173,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1172,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full inline-block mb-3\",\n                                                                        children: \"Notre Recommandation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1190,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2\",\n                                                                        children: \"Auto Backup\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1193,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-blue-600 font-bold text-lg mb-3\",\n                                                                        children: \"18 MAD/mois\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-600 mb-2 font-medium\",\n                                                                        children: \"Set it and forget it.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-500 text-sm mb-4\",\n                                                                        children: \"Data security with no effort\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1208,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"automated\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1209,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1207,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1214,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"daily\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1215,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1213,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1220,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"1-Click Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1221,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1219,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1226,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"10 days\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1229,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1225,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1206,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1189,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"none\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"none\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1254,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1249,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1248,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1247,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2 mt-8\",\n                                                                        children: \"None\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1265,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-600 font-bold text-lg mb-6\",\n                                                                        children: \"Free\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1268,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1274,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1275,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1273,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1280,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"on demand\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1281,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1279,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1286,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1287,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1285,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1292,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1295,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1291,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1272,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1264,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1238,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1154,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"5. Networking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Private Networking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1317,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: privateNetworking,\n                                                                        onChange: (e)=>setPrivateNetworking(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"No Private Networking\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1326,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"enabled\",\n                                                                                children: \"Private Networking Enabled\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1327,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1321,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: privateNetworking === \"enabled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"28 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1333,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1337,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1331,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1320,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Bandwidth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                        children: \"32 TB Out + Unlimited In\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1351,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs sm:text-sm text-purple-600\",\n                                                                        children: \"200 Mbit/s Connection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1354,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"IPv4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ipv4Addresses,\n                                                                        onChange: (e)=>setIpv4Addresses(parseInt(e.target.value)),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1,\n                                                                                children: \"1 IP Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1373,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 2,\n                                                                                children: \"1 IP Address + 1 Additional IP\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1374,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: ipv4Addresses === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"42 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1380,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1384,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1361,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1314,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1306,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"6. Add-Ons\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Object Storage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: objectStorage,\n                                                                        onChange: (e)=>setObjectStorage(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1417,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"250gb\",\n                                                                                children: \"250 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1418,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"500gb\",\n                                                                                children: \"500 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1419,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"750gb\",\n                                                                                children: \"750 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1420,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"1tb\",\n                                                                                children: \"1 TB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1421,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            objectStorage === \"250gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"50 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1425,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"500gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"95 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1430,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"750gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"140 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1435,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"1tb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"180 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1440,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1445,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1411,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Server Management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: serverManagement,\n                                                                        onChange: (e)=>setServerManagement(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"unmanaged\",\n                                                                                children: \"Unmanaged\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1464,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"managed\",\n                                                                                children: \"Managed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1465,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1459,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: serverManagement === \"unmanaged\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1469,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"1340 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1473,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1467,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1458,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Monitoring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1483,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: monitoring,\n                                                                        onChange: (e)=>setMonitoring(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1492,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"full\",\n                                                                                children: \"Full Monitoring\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1493,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1487,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: monitoring === \"full\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"140 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1497,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1501,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1495,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1486,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1482,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"SSL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1511,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ssl,\n                                                                        onChange: (e)=>setSsl(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1520,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"basic\",\n                                                                                children: \"SSL certificate\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1521,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"wildcard\",\n                                                                                children: \"SSL certificate (wildcard)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1522,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1515,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            ssl === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"914 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1529,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1532,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1528,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"wildcard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"2740 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1539,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1542,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1538,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1548,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1526,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1514,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1510,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1396,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 843,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:sticky lg:top-4 lg:max-h-[calc(100vh-2rem)] lg:overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg h-full flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6 flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6 flex-shrink-0\",\n                                                children: t(\"order_summary\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1565,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 sm:space-y-4 mb-4 sm:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base text-gray-600\",\n                                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1576,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: \"Quantit\\xe9:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1580,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                                                disabled: quantity === 1,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"-\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1584,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"w-8 text-center font-medium text-xs\",\n                                                                                                children: quantity\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1595,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.min(10, quantity + 1)),\n                                                                                                disabled: quantity === 10,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"+\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1598,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1583,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1579,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1575,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            ((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1612,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1574,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            additionalIPs > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"IPs additionnelles (\",\n                                                                            additionalIPs,\n                                                                            \" \\xd7 \",\n                                                                            quantity,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1619,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            additionalIPs * 15 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1622,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1618,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            !isAutoBackup && backupEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"Sauvegarde automatique \\xd7 \",\n                                                                            quantity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1630,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            20 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1633,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1629,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedPeriod !== \"monthly\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"R\\xe9duction (\",\n                                                                            selectedPeriod === \"6months\" ? \"3%\" : \"10%\",\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1641,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            \"-\",\n                                                                            Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity * (selectedPeriod === \"6months\" ? 6 * 0.03 : 12 * 0.1)),\n                                                                            \" \",\n                                                                            \"MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1645,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1640,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                className: \"border-gray-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1659,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-base sm:text-lg font-bold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: t(\"total\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1662,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 flex-shrink-0 text-right\",\n                                                                        children: [\n                                                                            Math.round(calculateTotal()),\n                                                                            \" MAD/\",\n                                                                            selectedPeriod === \"monthly\" ? \"mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"an\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1665,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1661,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs sm:text-sm text-gray-600 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"os_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1679,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_operatingSystems_find = operatingSystems.find((os)=>os.id === selectedOS)) === null || _operatingSystems_find === void 0 ? void 0 : _operatingSystems_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1680,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1678,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"location_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1689,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_locations_find = locations.find((loc)=>loc.id === selectedLocation)) === null || _locations_find === void 0 ? void 0 : _locations_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1690,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1688,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"P\\xe9riode:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1699,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: selectedPeriod === \"monthly\" ? \"1 mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"12 mois\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1700,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1698,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1677,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1676,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1573,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"lg\",\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold\",\n                                                        onClick: handleAddToCart,\n                                                        disabled: orderLoading || loading || !selectedPlan,\n                                                        children: orderLoading ? \"Ajout en cours...\" : \"Ajouter au panier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1712,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1723,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Paiement s\\xe9curis\\xe9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1724,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1722,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1727,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"D\\xe9ploiement en 5 minutes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1728,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1726,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1721,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1572,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1564,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1563,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 1562,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 1561,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 841,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 840,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 798,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigureVPSPage, \"SEfnX1C1tXnxpgT+9Zc9Ccbt8wQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations,\n        _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c4 = ConfigureVPSPage;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"UbuntuIcon\");\n$RefreshReg$(_c1, \"CentOSIcon\");\n$RefreshReg$(_c2, \"DebianIcon\");\n$RefreshReg$(_c3, \"WindowsIcon\");\n$RefreshReg$(_c4, \"ConfigureVPSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx\n"));

/***/ })

});