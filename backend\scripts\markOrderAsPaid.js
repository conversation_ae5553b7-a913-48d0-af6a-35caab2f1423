#!/usr/bin/env node

/**
 * <PERSON>ript to mark an order as paid for testing VPS provisioning
 * Usage: node markOrderAsPaid.js <orderId>
 * Example: node markOrderAsPaid.js 67890abcdef123456789
 */

const mongoose = require("mongoose");
const dotenv = require("dotenv");
const path = require("path");

// Load environment variables
dotenv.config({ path: path.join(__dirname, "../.env") });

// Import models and services
const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const orderService = require("../services/orderService");
const vpsService = require("../services/vpsService");

// Connect to database
async function connectDB() {
  try {
    // Use zn_tech database instead of the one in .env
    let mongoUri = process.env.MONGODB_URI || process.env.DB_URI;

    // Replace the database name with zn_tech
    if (mongoUri.includes("/ztech?")) {
      mongoUri = mongoUri.replace("/ztech?", "/zn_tech?");
    } else if (mongoUri.includes("/ztech")) {
      mongoUri = mongoUri.replace("/ztech", "/zn_tech");
    }

    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("✅ Connected to MongoDB (using zn_tech database)");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  }
}

// Main function to mark order as paid
async function markOrderAsPaid(orderId) {
  try {
    console.log(`🔍 Looking for order: ${orderId}`);

    // Find order by ID or identifiant
    let order = await Order.findById(orderId)
      .populate("user", "firstName lastName email")
      .populate({
        path: "subOrders",
        populate: { path: "package", model: "Package" },
      });

    // If not found by ID, try by identifiant
    if (!order) {
      order = await Order.findOne({ identifiant: orderId })
        .populate("user", "firstName lastName email")
        .populate({
          path: "subOrders",
          populate: { path: "package", model: "Package" },
        });
    }

    if (!order) {
      console.error(`❌ Order not found: ${orderId}`);
      return false;
    }

    console.log(`📋 Order found:`);
    console.log(`   - ID: ${order._id}`);
    console.log(`   - Identifiant: ${order.identifiant}`);
    console.log(
      `   - User: ${order.user.firstName} ${order.user.lastName} (${order.user.email})`
    );
    console.log(
      `   - Total Price: ${order.totalPrice} ${order.currency || "MAD"}`
    );
    console.log(`   - Current Status: ${order.status}`);
    console.log(`   - Is Paid: ${order.isPaid}`);
    console.log(`   - SubOrders: ${order.subOrders.length}`);

    // Check if already paid
    if (order.isPaid) {
      console.log(`⚠️  Order is already marked as paid`);
      console.log(`   - Paid Date: ${order.datePaid}`);
      console.log(`   - Transaction ID: ${order.transactionId}`);

      // Ask if user wants to continue anyway
      const readline = require("readline");
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
      });

      const answer = await new Promise((resolve) => {
        rl.question("Do you want to continue anyway? (y/N): ", resolve);
      });
      rl.close();

      if (answer.toLowerCase() !== "y" && answer.toLowerCase() !== "yes") {
        console.log("❌ Operation cancelled");
        return false;
      }
    }

    // Generate a test transaction ID
    const testTransactionId = `TEST_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    console.log(
      `💳 Marking order as paid with transaction ID: ${testTransactionId}`
    );

    // Use the orderService to mark as paid (this will trigger VPS provisioning)
    const success = await orderService.markOrderAsPaid(
      order._id,
      testTransactionId
    );

    if (success) {
      console.log("✅ Order successfully marked as paid!");

      // Check for VPS suborders and show provisioning status
      const vpsSubOrders = order.subOrders.filter((subOrder) => subOrder.vps);
      if (vpsSubOrders.length > 0) {
        console.log(`🖥️  VPS SubOrders found: ${vpsSubOrders.length}`);

        // Wait a moment for provisioning to start
        setTimeout(async () => {
          try {
            for (const subOrder of vpsSubOrders) {
              const updatedSubOrder = await SubOrder.findById(subOrder._id);
              console.log(`   - SubOrder ${updatedSubOrder.identifiant}:`);
              console.log(`     * Status: ${updatedSubOrder.status}`);
              console.log(
                `     * VPS Status: ${updatedSubOrder.vps?.status || "N/A"}`
              );
              console.log(
                `     * Provider: ${updatedSubOrder.vps?.provider || "N/A"}`
              );
              console.log(
                `     * Plan ID: ${updatedSubOrder.vps?.planId || "N/A"}`
              );
              console.log(
                `     * Region: ${updatedSubOrder.vps?.region || "N/A"}`
              );
              console.log(
                `     * OS: ${updatedSubOrder.vps?.operatingSystem || "N/A"}`
              );

              if (updatedSubOrder.vps?.ipAddress) {
                console.log(
                  `     * IP Address: ${updatedSubOrder.vps.ipAddress}`
                );
              }
              if (updatedSubOrder.vps?.providerInstanceId) {
                console.log(
                  `     * Provider Instance ID: ${updatedSubOrder.vps.providerInstanceId}`
                );
              }
            }
          } catch (error) {
            console.error("Error checking VPS status:", error.message);
          }

          // Close database connection
          await mongoose.connection.close();
          console.log("🔌 Database connection closed");
        }, 2000);
      } else {
        // Close database connection immediately if no VPS orders
        await mongoose.connection.close();
        console.log("🔌 Database connection closed");
      }

      return true;
    } else {
      console.error("❌ Failed to mark order as paid");
      return false;
    }
  } catch (error) {
    console.error("❌ Error marking order as paid:", error);
    return false;
  }
}

// Main execution
async function main() {
  const orderId = process.argv[2];

  if (!orderId) {
    console.error("❌ Please provide an order ID");
    console.log("Usage: node markOrderAsPaid.js <orderId>");
    console.log("Example: node markOrderAsPaid.js 67890abcdef123456789");
    console.log("Example: node markOrderAsPaid.js ORD-2024-001");
    process.exit(1);
  }

  await connectDB();

  const success = await markOrderAsPaid(orderId);

  if (!success) {
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Script execution failed:", error);
    process.exit(1);
  });
}

module.exports = { markOrderAsPaid };
