"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/vps/configure/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx":
/*!***********************************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfigureVPSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/vpsService */ \"(app-pages-browser)/./src/app/services/vpsService.js\");\n/* harmony import */ var _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/cartService */ \"(app-pages-browser)/./src/app/services/cartService.js\");\n/* harmony import */ var _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/packageService */ \"(app-pages-browser)/./src/app/services/packageService.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/context/AuthContext */ \"(app-pages-browser)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Modern OS Icons Components\nconst UbuntuIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n};\n_c = UbuntuIcon;\nconst CentOSIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = CentOSIcon;\nconst DebianIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = DebianIcon;\nconst WindowsIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 76,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = WindowsIcon;\nfunction ConfigureVPSPage() {\n    var _operatingSystems_find, _locations_find;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"vps_configure\");\n    const { setCartCount } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // State management\n    const [vpsPlans, setVpsPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [osImages, setOsImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [regions, setRegions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderLoading, setOrderLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize plan from URL params\n    const planId = searchParams.get(\"plan\");\n    const autoBackup = searchParams.get(\"autobackup\") === \"true\";\n    const [selectedPlan, setSelectedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAutoBackup, setIsAutoBackup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    // Function to parse specifications from database\n    const parseSpecifications = (specifications, description)=>{\n        let cores = 0, ram = \"0 GB\", storage = \"0 GB\", traffic = \"32 TB\";\n        // Parse from specifications array\n        if (specifications && Array.isArray(specifications)) {\n            specifications.forEach((spec)=>{\n                const value = spec.value || \"\";\n                const lowerValue = value.toLowerCase();\n                // Parse CPU cores\n                if (lowerValue.includes(\"cpu\") || lowerValue.includes(\"core\") || lowerValue.includes(\"vcpu\")) {\n                    const cpuMatch = value.match(/(\\d+)/);\n                    if (cpuMatch) cores = parseInt(cpuMatch[1]);\n                }\n                // Parse RAM\n                if (lowerValue.includes(\"ram\") || lowerValue.includes(\"memory\") || lowerValue.includes(\"gb ram\")) {\n                    const ramMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n                }\n                // Parse Storage\n                if (lowerValue.includes(\"storage\") || lowerValue.includes(\"disk\") || lowerValue.includes(\"ssd\") || lowerValue.includes(\"nvme\")) {\n                    const storageMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (storageMatch) {\n                        const storageType = lowerValue.includes(\"nvme\") ? \"NVMe\" : lowerValue.includes(\"ssd\") ? \"SSD\" : \"\";\n                        storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n                    }\n                }\n                // Parse Traffic/Bandwidth\n                if (lowerValue.includes(\"traffic\") || lowerValue.includes(\"bandwidth\") || lowerValue.includes(\"transfer\")) {\n                    const trafficMatch = value.match(/(\\d+)\\s*(tb|gb)/i);\n                    if (trafficMatch) {\n                        traffic = \"\".concat(trafficMatch[1], \" \").concat(trafficMatch[2].toUpperCase());\n                    }\n                }\n            });\n        }\n        // Fallback: parse from description if specifications are empty\n        if (cores === 0 && description) {\n            const descLower = description.toLowerCase();\n            const cpuMatch = description.match(/(\\d+)\\s*(cpu|core|vcpu)/i);\n            if (cpuMatch) cores = parseInt(cpuMatch[1]);\n            const ramMatch = description.match(/(\\d+)\\s*gb\\s*ram/i);\n            if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n            const storageMatch = description.match(/(\\d+)\\s*gb\\s*(storage|disk|ssd|nvme)/i);\n            if (storageMatch) {\n                const storageType = descLower.includes(\"nvme\") ? \"NVMe\" : descLower.includes(\"ssd\") ? \"SSD\" : \"\";\n                storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n            }\n        }\n        return {\n            cores,\n            ram,\n            storage,\n            traffic\n        };\n    };\n    // Fetch VPS packages and find the selected one\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchVPSPackages = async ()=>{\n            try {\n                setLoading(true);\n                // Récupérer les packages VPS depuis la base de données\n                const response = await _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPackages(\"VPS Hosting\");\n                console.log(\"VPS packages response:\", response);\n                let vpsPackages = [];\n                if (response.data && Array.isArray(response.data)) {\n                    vpsPackages = response.data;\n                } else if (response.data && response.data.packages && Array.isArray(response.data.packages)) {\n                    vpsPackages = response.data.packages;\n                } else if (Array.isArray(response)) {\n                    vpsPackages = response;\n                }\n                // Transformer les packages de la base de données\n                const transformedPlans = vpsPackages.map((pkg)=>{\n                    const specs = parseSpecifications(pkg.specifications, pkg.description);\n                    return {\n                        id: pkg._id,\n                        _id: pkg._id,\n                        name: pkg.name,\n                        price: pkg.price,\n                        cores: specs.cores,\n                        ram: specs.ram,\n                        storage: specs.storage,\n                        traffic: specs.traffic,\n                        description: pkg.description,\n                        specifications: pkg.specifications\n                    };\n                });\n                setVpsPlans(transformedPlans);\n                // Trouver le package sélectionné par son ID\n                if (planId && transformedPlans.length > 0) {\n                    const foundPlan = transformedPlans.find((plan)=>{\n                        var _plan__id, _plan_id;\n                        return plan._id === planId || plan.id === planId || ((_plan__id = plan._id) === null || _plan__id === void 0 ? void 0 : _plan__id.toString()) === planId || ((_plan_id = plan.id) === null || _plan_id === void 0 ? void 0 : _plan_id.toString()) === planId;\n                    });\n                    if (foundPlan) {\n                        setSelectedPlan(foundPlan);\n                        console.log(\"Selected plan found:\", foundPlan);\n                    } else {\n                        console.error(\"Plan not found with ID:\", planId);\n                        console.log(\"Available plans:\", transformedPlans.map((p)=>({\n                                id: p.id,\n                                _id: p._id,\n                                name: p.name\n                            })));\n                        setError(\"Package VPS non trouv\\xe9\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error fetching VPS plans:\", error);\n                setError(\"Erreur lors du chargement des plans VPS\");\n                setVpsPlans([]);\n                setSelectedPlan(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        // Fetch dynamic OS images from API\n        const fetchOSImages = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching OS images from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                console.log(\"✅ OS Images response:\", response);\n                let images = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    images = response.data.data;\n                } else if (response.data && response.data.images && Array.isArray(response.data.images)) {\n                    images = response.data.images;\n                }\n                // Transform API data to expected format\n                const transformedImages = images.map((img)=>({\n                        id: img.imageId || img.id,\n                        name: img.name,\n                        description: img.description,\n                        type: img.osType || \"linux\",\n                        version: img.version,\n                        provider: img.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting OS images:\", transformedImages.length, \"images\");\n                setOsImages(transformedImages);\n                // Set default OS if available\n                if (transformedImages.length > 0) {\n                    const defaultOS = transformedImages.find((img)=>img.name.toLowerCase().includes(\"ubuntu\") && img.name.toLowerCase().includes(\"22.04\")) || transformedImages[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default OS:\", defaultOS.name, defaultOS.id);\n                    setSelectedOS(defaultOS.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching OS images:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticOsImages = [\n                    {\n                        id: \"ubuntu-20.04\",\n                        name: \"Ubuntu 20.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"ubuntu-22.04\",\n                        name: \"Ubuntu 22.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"centos-7\",\n                        name: \"CentOS 7\",\n                        type: \"centos\"\n                    },\n                    {\n                        id: \"debian-11\",\n                        name: \"Debian 11\",\n                        type: \"debian\"\n                    }\n                ];\n                setOsImages(staticOsImages);\n                setSelectedOS(\"ubuntu-22.04\");\n            }\n        };\n        // Fetch dynamic regions from API\n        const fetchRegions = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching regions from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                console.log(\"✅ Regions response:\", response);\n                let regions = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    regions = response.data.data;\n                } else if (response.data && response.data.regions && Array.isArray(response.data.regions)) {\n                    regions = response.data.regions;\n                }\n                // Transform API data to expected format\n                const transformedRegions = regions.map((region)=>({\n                        id: region.regionSlug,\n                        name: region.regionName,\n                        provider: region.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting regions:\", transformedRegions.length, \"regions\");\n                setRegions(transformedRegions);\n                // Set default region if available\n                if (transformedRegions.length > 0) {\n                    const defaultRegion = transformedRegions.find((region)=>region.id === \"EU\") || transformedRegions[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default region:\", defaultRegion.name, defaultRegion.id);\n                    setSelectedLocation(defaultRegion.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching regions:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticRegions = [\n                    {\n                        id: \"EU\",\n                        name: \"European Union\",\n                        description: \"Germany\",\n                        country: \"Germany\",\n                        city: \"Nuremberg\"\n                    },\n                    {\n                        id: \"US-central\",\n                        name: \"United States Central\",\n                        description: \"St. Louis\",\n                        country: \"United States\",\n                        city: \"St. Louis\"\n                    },\n                    {\n                        id: \"SG\",\n                        name: \"Asia Pacific\",\n                        description: \"Singapore\",\n                        country: \"Singapore\",\n                        city: \"Singapore\"\n                    }\n                ];\n                setRegions(staticRegions);\n                setSelectedLocation(\"EU\");\n            }\n        };\n        // Appeler les fonctions pour récupérer les données\n        fetchVPSPackages();\n        fetchOSImages();\n        fetchRegions();\n    }, [\n        planId\n    ]);\n    // Configuration state\n    const [selectedOS, setSelectedOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ubuntu-20.04\");\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"france\");\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [additionalIPs, setAdditionalIPs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [backupEnabled, setBackupEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // New Contabo-style options\n    const [autoBackupOption, setAutoBackupOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [privateNetworking, setPrivateNetworking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ipv4Addresses, setIpv4Addresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [objectStorage, setObjectStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [serverManagement, setServerManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"unmanaged\");\n    const [monitoring, setMonitoring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ssl, setSsl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    // Login & Password section state\n    const [serverUsername, setServerUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"root\");\n    const [serverPassword, setServerPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Handle adding VPS to cart\n    const handleAddToCart = async ()=>{\n        if (!selectedPlan) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez s\\xe9lectionner un plan VPS\");\n            return;\n        }\n        try {\n            var _selectedPlan_vpsConfig, _response_data_cart, _response_data;\n            setOrderLoading(true);\n            // Vérifier si nous avons un ID valide\n            const packageId = selectedPlan._id || selectedPlan.id;\n            if (!packageId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"ID du package VPS manquant. Veuillez r\\xe9essayer.\");\n                return;\n            }\n            // Map frontend selections to Contabo API format\n            const contaboRegionMap = {\n                france: \"EU\",\n                EU: \"EU\",\n                germany: \"EU\",\n                \"US-central\": \"US-east\",\n                usa: \"US-east\",\n                SG: \"SIN\",\n                singapore: \"SIN\",\n                asia: \"SIN\"\n            };\n            const contaboOSMap = {\n                \"ubuntu-20.04\": \"ubuntu-20.04\",\n                \"ubuntu-22.04\": \"ubuntu-22.04\",\n                \"ubuntu-24.04\": \"ubuntu-24.04\",\n                \"centos-7\": \"centos-7\",\n                \"centos-8\": \"centos-8\",\n                \"debian-10\": \"debian-10\",\n                \"debian-11\": \"debian-11\",\n                \"windows-2019\": \"windows-server-2019\",\n                \"windows-2022\": \"windows-server-2022\"\n            };\n            // Generate display name if not provided\n            const displayName = \"\".concat(selectedPlan.name, \"-\").concat(Date.now());\n            // Préparer les données pour l'ajout au panier avec configuration Contabo\n            const cartData = {\n                packageId: packageId,\n                quantity: quantity,\n                period: selectedPeriod === \"monthly\" ? 1 : selectedPeriod === \"6months\" ? 6 : 12,\n                // Configuration personnalisée pour Contabo VPS\n                customConfiguration: {\n                    // Contabo API fields\n                    planId: ((_selectedPlan_vpsConfig = selectedPlan.vpsConfig) === null || _selectedPlan_vpsConfig === void 0 ? void 0 : _selectedPlan_vpsConfig.providerProductId) || selectedPlan.id,\n                    provider: \"contabo\",\n                    region: contaboRegionMap[selectedLocation] || \"EU\",\n                    operatingSystem: contaboOSMap[selectedOS] || selectedOS,\n                    displayName: displayName,\n                    sshKeys: [],\n                    userData: \"\",\n                    addons: {\n                        privatenetworking: privateNetworking !== \"none\",\n                        autobackup: autoBackupOption !== \"none\",\n                        monitoring: monitoring !== \"none\"\n                    },\n                    // Plan specifications for reference\n                    cpu: selectedPlan.cores || selectedPlan.cpu,\n                    ram: selectedPlan.ram,\n                    storage: selectedPlan.storage,\n                    bandwidth: selectedPlan.traffic || selectedPlan.bandwidth,\n                    // Frontend-specific fields for display\n                    frontendConfig: {\n                        operatingSystem: selectedOS,\n                        location: selectedLocation,\n                        additionalIPs: additionalIPs,\n                        backup: isAutoBackup || backupEnabled,\n                        planName: selectedPlan.name,\n                        autoBackupOption: autoBackupOption,\n                        privateNetworking: privateNetworking,\n                        ipv4Addresses: ipv4Addresses,\n                        objectStorage: objectStorage,\n                        serverManagement: serverManagement,\n                        monitoring: monitoring,\n                        ssl: ssl\n                    }\n                }\n            };\n            console.log(\"Adding VPS to cart:\", cartData);\n            console.log(\"Selected plan:\", selectedPlan);\n            // Ajouter au panier via le service\n            const response = await _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addItemToCart(cartData);\n            console.log(\"Cart response:\", response);\n            // Mettre à jour le compteur du panier\n            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_cart = _response_data.cart) === null || _response_data_cart === void 0 ? void 0 : _response_data_cart.cartCount) {\n                setCartCount(response.data.cart.cartCount);\n            }\n            // Afficher le message de succès\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(selectedPlan.name, \" ajout\\xe9 au panier avec succ\\xe8s!\"));\n            // Rediriger vers le panier\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response2, _error_response3, _error_response4;\n            console.error(\"Error adding VPS to cart:\", error);\n            console.error(\"Error response:\", error.response);\n            console.error(\"Error data:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // Gestion des erreurs spécifiques\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.response.data.message);\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Package VPS non trouv\\xe9. Veuillez contacter le support.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez vous connecter pour ajouter au panier.\");\n                router.push(\"/auth/login\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Donn\\xe9es invalides. Veuillez v\\xe9rifier votre s\\xe9lection.\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Erreur lors de l'ajout au panier. Veuillez r\\xe9essayer.\");\n            }\n        } finally{\n            setOrderLoading(false);\n        }\n    };\n    // Use dynamic OS images data with fallback to static data\n    const operatingSystems = osImages.length > 0 ? osImages.map((os)=>({\n            id: os.id,\n            name: os.name,\n            icon: getOSIcon(os.type || os.osType),\n            type: os.type || os.osType || \"linux\",\n            description: os.description,\n            version: os.version,\n            provider: os.provider\n        })) : [\n        {\n            id: \"ubuntu-20.04\",\n            name: \"Ubuntu 20.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"ubuntu-22.04\",\n            name: \"Ubuntu 22.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"centos-8\",\n            name: \"CentOS 8\",\n            icon: CentOSIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"debian-11\",\n            name: \"Debian 11\",\n            icon: DebianIcon,\n            type: \"linux\"\n        }\n    ];\n    // Helper function to get OS icon based on type\n    function getOSIcon(osType) {\n        const iconMap = {\n            ubuntu: UbuntuIcon,\n            centos: CentOSIcon,\n            debian: DebianIcon,\n            windows: WindowsIcon,\n            linux: UbuntuIcon\n        };\n        // Check if osType contains specific OS names\n        if (osType && typeof osType === \"string\") {\n            const lowerType = osType.toLowerCase();\n            if (lowerType.includes(\"ubuntu\")) return UbuntuIcon;\n            if (lowerType.includes(\"centos\")) return CentOSIcon;\n            if (lowerType.includes(\"debian\")) return DebianIcon;\n            if (lowerType.includes(\"windows\")) return WindowsIcon;\n        }\n        return iconMap[osType] || UbuntuIcon;\n    }\n    // Use dynamic regions data with fallback to static data\n    const locations = regions.length > 0 ? regions.map((region)=>({\n            id: region.id,\n            name: region.name,\n            flag: getRegionFlag(region.country),\n            ping: getRegionPing(region.id),\n            description: region.description,\n            city: region.city,\n            country: region.country\n        })) : [\n        {\n            id: \"EU\",\n            name: \"European Union\",\n            flag: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            ping: \"15ms\",\n            description: \"Germany\",\n            city: \"Nuremberg\",\n            country: \"Germany\"\n        },\n        {\n            id: \"US-central\",\n            name: \"United States Central\",\n            flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            ping: \"120ms\",\n            description: \"St. Louis\",\n            city: \"St. Louis\",\n            country: \"United States\"\n        },\n        {\n            id: \"SG\",\n            name: \"Asia Pacific\",\n            flag: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            ping: \"200ms\",\n            description: \"Singapore\",\n            city: \"Singapore\",\n            country: \"Singapore\"\n        }\n    ];\n    // Helper functions for region display\n    function getRegionFlag(country) {\n        const flagMap = {\n            Germany: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            \"United States\": \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            Singapore: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            France: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            Netherlands: \"\\uD83C\\uDDF3\\uD83C\\uDDF1\"\n        };\n        return flagMap[country] || \"\\uD83C\\uDF0D\";\n    }\n    function getRegionPing(regionId) {\n        const pingMap = {\n            EU: \"15ms\",\n            \"US-central\": \"120ms\",\n            \"US-east\": \"110ms\",\n            \"US-west\": \"130ms\",\n            SG: \"200ms\"\n        };\n        return pingMap[regionId] || \"50ms\";\n    }\n    const calculateTotal = ()=>{\n        if (!selectedPlan) return 0;\n        let total = selectedPlan.price;\n        // Additional IPs cost\n        total += additionalIPs * 15; // 15 MAD per additional IP\n        // Backup cost (only if not auto backup plan)\n        if (!isAutoBackup && backupEnabled) {\n            total += 20; // 20 MAD for backup\n        }\n        // Auto Backup cost (Contabo style)\n        if (autoBackupOption === \"auto\") {\n            total += 18; // €1.79 ≈ 18 MAD per month\n        }\n        // Private Networking cost\n        if (privateNetworking === \"enabled\") {\n            total += 28; // 28 MAD per month\n        }\n        // IPv4 additional addresses cost\n        if (ipv4Addresses === 2) {\n            total += 42; // 42 MAD per additional IP\n        }\n        // Object Storage cost\n        const objectStorageCosts = {\n            \"250gb\": 50,\n            \"500gb\": 95,\n            \"750gb\": 140,\n            \"1tb\": 180\n        };\n        if (objectStorage !== \"none\" && objectStorageCosts[objectStorage]) {\n            total += objectStorageCosts[objectStorage];\n        }\n        // Server Management cost\n        if (serverManagement === \"managed\") {\n            total += 1340; // 1340 MAD per month for managed\n        }\n        // Monitoring cost\n        if (monitoring === \"full\") {\n            total += 140; // 140 MAD per month for full monitoring\n        }\n        // SSL cost (one-time charges, but we'll add monthly equivalent)\n        const sslCosts = {\n            basic: 76,\n            wildcard: 228\n        };\n        if (ssl !== \"none\" && sslCosts[ssl]) {\n            total += sslCosts[ssl];\n        }\n        // Apply quantity\n        total *= quantity;\n        // Period multiplier\n        const multipliers = {\n            monthly: 1,\n            \"6months\": 6 * 0.97,\n            annually: 12 * 0.9\n        };\n        return total * multipliers[selectedPeriod];\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 773,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600\",\n                        children: \"Loading VPS configuration...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 774,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 772,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 771,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-red-600 mb-2\",\n                        children: \"Error loading VPS configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 787,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 790,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        color: \"blue\",\n                        size: \"sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 791,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 786,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 785,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 sm:gap-4 w-full sm:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outlined\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.history.back(),\n                                        className: \"border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: t(\"back\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h4\",\n                                                className: \"text-lg sm:text-2xl text-gray-900 font-bold truncate\",\n                                                children: t(\"page_title\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 truncate\",\n                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 809,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left sm:text-right w-full sm:w-auto flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                        children: t(\"price_from\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        variant: \"h3\",\n                                        className: \"text-lg sm:text-2xl text-blue-600 font-bold\",\n                                        children: [\n                                            (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0,\n                                            \" MAD\",\n                                            t(\"per_month\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 831,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 808,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 807,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 806,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6 sm:space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 sm:w-6 h-5 sm:h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                variant: \"h5\",\n                                                                className: \"text-lg sm:text-xl text-gray-900 font-bold truncate\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isAutoBackup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 866,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                        children: \"Auto Backup Inclus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 867,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"vcpu_cores\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.cores) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 886,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"ram\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 887,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.ram) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 895,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"storage\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.storage) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"traffic\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.traffic) || \"0 TB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 908,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 851,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"1. \",\n                                                    t(\"billing_period\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"billing_period_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        id: \"monthly\",\n                                                        label: \"1 mois\",\n                                                        discount: \"\",\n                                                        price: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0\n                                                    },\n                                                    {\n                                                        id: \"6months\",\n                                                        label: \"6 mois\",\n                                                        discount: \"3% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 6 * 0.97)\n                                                    },\n                                                    {\n                                                        id: \"annually\",\n                                                        label: \"12 mois\",\n                                                        discount: \"10% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 12 * 0.9)\n                                                    }\n                                                ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedPeriod(period.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 rounded-full border-2 flex-shrink-0 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-600\" : \"border-gray-300\"),\n                                                                            children: selectedPeriod === period.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 968,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 960,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                                    children: period.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 972,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                period.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                                    children: period.discount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 976,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 971,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 959,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                            children: [\n                                                                                period.price,\n                                                                                \" MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: period.id === \"monthly\" ? \"/mois\" : period.id === \"6months\" ? \"/6 mois\" : \"/an\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 982,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, period.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"2. \",\n                                                    t(\"choose_os\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1004,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_os_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1010,\n                                                columnNumber: 17\n                                            }, this),\n                                            osImages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des syst\\xe8mes d'exploitation...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            operatingSystems.length,\n                                                            \" OS disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching OS images...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                                                                console.log(\"✅ Force fetch result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des OS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1014,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        osImages.length,\n                                                        \" syst\\xe8mes d'exploitation charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 1040,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: operatingSystems.map((os)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedOS(os.id),\n                                                        className: \"relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedOS === os.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 sm:gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(os.icon, {\n                                                                    className: \"w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1058,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-sm sm:text-base text-gray-900 break-words\",\n                                                                            children: os.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1060,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                                                            children: os.type === \"linux\" ? \"Linux Distribution\" : \"Windows Server\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1063,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1059,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedOS === os.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1070,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 1057,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, os.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1003,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"3. \",\n                                                    t(\"choose_location\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_location_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1088,\n                                                columnNumber: 17\n                                            }, this),\n                                            regions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des emplacements...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1093,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            locations.length,\n                                                            \" emplacements disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1096,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching regions...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                                                                console.log(\"✅ Force fetch regions result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch regions error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-green-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des r\\xe9gions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        regions.length,\n                                                        \" emplacements charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1120,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: locations.map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedLocation(location.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedLocation === location.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl sm:text-2xl flex-shrink-0\",\n                                                                            children: location.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1139,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium text-sm sm:text-base text-gray-900 truncate\",\n                                                                                children: location.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1143,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1142,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1138,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedLocation === location.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 sm:w-5 h-4 sm:h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1149,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 1137,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, location.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1080,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"4. Data Protection with Auto Backup\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"auto\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"auto\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"auto\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1185,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1180,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1179,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full inline-block mb-3\",\n                                                                        children: \"Notre Recommandation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2\",\n                                                                        children: \"Auto Backup\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1199,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-blue-600 font-bold text-lg mb-3\",\n                                                                        children: \"18 MAD/mois\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1202,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-600 mb-2 font-medium\",\n                                                                        children: \"Set it and forget it.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1205,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-500 text-sm mb-4\",\n                                                                        children: \"Data security with no effort\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1208,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1214,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"automated\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1215,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1213,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1220,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"daily\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1221,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1219,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1226,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"1-Click Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1227,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1225,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1232,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"10 days\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1235,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1231,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1212,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"none\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"none\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1260,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1255,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1254,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1253,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2 mt-8\",\n                                                                        children: \"None\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1271,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-600 font-bold text-lg mb-6\",\n                                                                        children: \"Free\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1274,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1280,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1281,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1279,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1286,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"on demand\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1287,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1285,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1292,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1293,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1291,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1298,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1301,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1297,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1278,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"5. Networking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Private Networking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1323,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: privateNetworking,\n                                                                        onChange: (e)=>setPrivateNetworking(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"No Private Networking\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1332,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"enabled\",\n                                                                                children: \"Private Networking Enabled\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1333,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1327,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: privateNetworking === \"enabled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"28 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1339,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1343,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1337,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1326,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1322,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Bandwidth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                        children: \"32 TB Out + Unlimited In\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1357,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs sm:text-sm text-purple-600\",\n                                                                        children: \"200 Mbit/s Connection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1360,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1356,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"IPv4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1368,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ipv4Addresses,\n                                                                        onChange: (e)=>setIpv4Addresses(parseInt(e.target.value)),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1,\n                                                                                children: \"1 IP Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1379,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 2,\n                                                                                children: \"1 IP Address + 1 Additional IP\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1380,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1372,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: ipv4Addresses === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"42 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1386,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1390,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1367,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1320,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1312,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"6. Add-Ons\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Object Storage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: objectStorage,\n                                                                        onChange: (e)=>setObjectStorage(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1423,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"250gb\",\n                                                                                children: \"250 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1424,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"500gb\",\n                                                                                children: \"500 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1425,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"750gb\",\n                                                                                children: \"750 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1426,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"1tb\",\n                                                                                children: \"1 TB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1427,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1418,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            objectStorage === \"250gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"50 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1431,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"500gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"95 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1436,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"750gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"140 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1441,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"1tb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"180 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1446,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1451,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1429,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1417,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1413,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Server Management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: serverManagement,\n                                                                        onChange: (e)=>setServerManagement(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"unmanaged\",\n                                                                                children: \"Unmanaged\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1470,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"managed\",\n                                                                                children: \"Managed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1471,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1465,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: serverManagement === \"unmanaged\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1475,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"1340 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1479,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1473,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1464,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1460,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Monitoring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1489,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: monitoring,\n                                                                        onChange: (e)=>setMonitoring(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1498,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"full\",\n                                                                                children: \"Full Monitoring\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1499,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1493,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: monitoring === \"full\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"140 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1503,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1507,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1501,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1492,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1488,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"SSL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1517,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ssl,\n                                                                        onChange: (e)=>setSsl(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1526,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"basic\",\n                                                                                children: \"SSL certificate\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1527,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"wildcard\",\n                                                                                children: \"SSL certificate (wildcard)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1528,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1521,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            ssl === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"914 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1535,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1538,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1534,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"wildcard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"2740 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1545,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1548,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1544,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1554,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1532,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1403,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 849,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:sticky lg:top-4 lg:max-h-[calc(100vh-2rem)] lg:overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg h-full flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6 flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6 flex-shrink-0\",\n                                                children: t(\"order_summary\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 sm:space-y-4 mb-4 sm:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base text-gray-600\",\n                                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: \"Quantit\\xe9:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1586,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                                                disabled: quantity === 1,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"-\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1590,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"w-8 text-center font-medium text-xs\",\n                                                                                                children: quantity\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1601,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.min(10, quantity + 1)),\n                                                                                                disabled: quantity === 10,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"+\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1604,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1589,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1585,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1581,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            ((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1618,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1580,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            additionalIPs > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"IPs additionnelles (\",\n                                                                            additionalIPs,\n                                                                            \" \\xd7 \",\n                                                                            quantity,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1625,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            additionalIPs * 15 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1628,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1624,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            !isAutoBackup && backupEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"Sauvegarde automatique \\xd7 \",\n                                                                            quantity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1636,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            20 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1639,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1635,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedPeriod !== \"monthly\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"R\\xe9duction (\",\n                                                                            selectedPeriod === \"6months\" ? \"3%\" : \"10%\",\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1647,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            \"-\",\n                                                                            Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity * (selectedPeriod === \"6months\" ? 6 * 0.03 : 12 * 0.1)),\n                                                                            \" \",\n                                                                            \"MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1651,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1646,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                className: \"border-gray-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1665,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-base sm:text-lg font-bold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: t(\"total\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1668,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 flex-shrink-0 text-right\",\n                                                                        children: [\n                                                                            Math.round(calculateTotal()),\n                                                                            \" MAD/\",\n                                                                            selectedPeriod === \"monthly\" ? \"mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"an\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1667,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs sm:text-sm text-gray-600 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"os_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1685,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_operatingSystems_find = operatingSystems.find((os)=>os.id === selectedOS)) === null || _operatingSystems_find === void 0 ? void 0 : _operatingSystems_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1686,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1684,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"location_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1695,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_locations_find = locations.find((loc)=>loc.id === selectedLocation)) === null || _locations_find === void 0 ? void 0 : _locations_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1696,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1694,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"P\\xe9riode:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1705,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: selectedPeriod === \"monthly\" ? \"1 mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"12 mois\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1706,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1704,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1683,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1682,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1579,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"lg\",\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold\",\n                                                        onClick: handleAddToCart,\n                                                        disabled: orderLoading || loading || !selectedPlan,\n                                                        children: orderLoading ? \"Ajout en cours...\" : \"Ajouter au panier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1718,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1729,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Paiement s\\xe9curis\\xe9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1730,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1728,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1733,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"D\\xe9ploiement en 5 minutes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1734,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1727,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1578,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1570,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1569,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 1568,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 1567,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 847,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 846,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 804,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigureVPSPage, \"5wTYs8D4HXndR5VBB5HKmg6p86Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations,\n        _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c4 = ConfigureVPSPage;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"UbuntuIcon\");\n$RefreshReg$(_c1, \"CentOSIcon\");\n$RefreshReg$(_c2, \"DebianIcon\");\n$RefreshReg$(_c3, \"WindowsIcon\");\n$RefreshReg$(_c4, \"ConfigureVPSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx\n"));

/***/ })

});