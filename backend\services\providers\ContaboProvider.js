/**
 * Contabo VPS Provider Implementation
 * Implements VPSProviderInterface for Contabo API
 * Follows Single Responsibility Principle (SRP) from SOLID
 */

const VPSProviderInterface = require("./VPSProviderInterface");
const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
require("dotenv").config();

class ContaboProvider extends VPSProviderInterface {
  constructor() {
    super();
    this.baseURL = process.env.CONTABO_API_URL || "https://api.contabo.com/v1";
    this.clientId = process.env.CONTABO_CLIENT_ID;
    this.clientSecret = process.env.CONTABO_CLIENT_SECRET;
    this.username = process.env.CONTABO_USERNAME;
    this.password = process.env.CONTABO_PASSWORD;
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Authenticate with Contabo API using OAuth2
   * @returns {Promise<string>} Access token
   */
  async authenticate() {
    try {
      if (
        this.accessToken &&
        this.tokenExpiry &&
        Date.now() < this.tokenExpiry
      ) {
        return this.accessToken;
      }

      // Use URLSearchParams for proper form-encoded data
      const params = new URLSearchParams();
      params.append("client_id", this.clientId);
      params.append("client_secret", this.clientSecret);
      params.append("username", this.username);
      params.append("password", this.password);
      params.append("grant_type", "password");

      const response = await axios.post(
        `https://auth.contabo.com/auth/realms/contabo/protocol/openid-connect/token`,
        params,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "x-request-id": uuidv4(),
          },
        }
      );

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + response.data.expires_in * 1000;

      return this.accessToken;
    } catch (error) {
      console.error(
        "Contabo authentication failed:",
        error.response?.data || error.message
      );
      throw new Error("Failed to authenticate with Contabo API");
    }
  }

  /**
   * Make authenticated API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise<Object>} API response
   */
  async makeRequest(method, endpoint, data = null) {
    try {
      const token = await this.authenticate();

      // Generate valid UUID4 for x-request-id header (required by Contabo)
      const requestId = uuidv4();

      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
          "x-request-id": requestId,
        },
      };

      if (data) {
        config.data = data;
      }

      console.log(`Contabo API Request [${requestId}]: ${method} ${endpoint}`);
      const response = await axios(config);
      console.log(`Contabo API Response [${requestId}]: ${response.status}`);

      return response.data;
    } catch (error) {
      const requestId = error.config?.headers?.["x-request-id"] || "unknown";
      console.error(
        `Contabo API request failed [${requestId}]: ${method} ${endpoint}`,
        error.response?.data || error.message
      );
      throw new Error(
        `Contabo API request failed: ${
          error.response?.data?.message || error.message
        }`
      );
    }
  }

  /**
   * Get available VPS plans from Contabo
   * Based on official Contabo API documentation - using actual product IDs
   * @returns {Promise<Array>} Array of VPS plans
   */
  async getPlans() {
    try {
      // According to Contabo API docs, these are the actual product IDs and specifications
      const contaboPlans = [
        // VPS 10 Series
        {
          id: "V91",
          productId: "V91",
          name: "VPS 10 NVMe",
          description: "VPS 10 with 75 GB NVMe SSD",
          cpu: 1,
          ram: 4,
          storage: 75,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 4.99, hourly: 0.007 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V92",
          productId: "V92",
          name: "VPS 10 SSD",
          description: "VPS 10 with 150 GB SSD",
          cpu: 1,
          ram: 4,
          storage: 150,
          diskType: "SSD",
          bandwidth: 32000,
          price: { monthly: 4.99, hourly: 0.007 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V94",
          productId: "V94",
          name: "VPS 20 NVMe",
          description: "VPS 20 with 100 GB NVMe SSD",
          cpu: 2,
          ram: 8,
          storage: 100,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 8.99, hourly: 0.013 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V95",
          productId: "V95",
          name: "VPS 20 SSD",
          description: "VPS 20 with 200 GB SSD",
          cpu: 2,
          ram: 8,
          storage: 200,
          diskType: "SSD",
          bandwidth: 32000,
          price: { monthly: 8.99, hourly: 0.013 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V97",
          productId: "V97",
          name: "VPS 30 NVMe",
          description: "VPS 30 with 200 GB NVMe SSD",
          cpu: 4,
          ram: 16,
          storage: 200,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 16.99, hourly: 0.025 },
          provider: "contabo",
          productType: "vps",
        },
        {
          id: "V8",
          productId: "V8",
          name: "VDS S",
          description: "VDS S with 180 GB NVMe",
          cpu: 2,
          ram: 8,
          storage: 180,
          diskType: "NVMe",
          bandwidth: 32000,
          price: { monthly: 19.99, hourly: 0.03 },
          provider: "contabo",
          productType: "vds",
        },
      ];

      // Add regions to all plans
      contaboPlans.forEach((plan) => {
        plan.regions = ["European Union", "United States", "Asia Pacific"];
      });

      return contaboPlans;
    } catch (error) {
      console.error("Failed to fetch Contabo plans:", error.message);
      throw error;
    }
  }

  /**
   * Get VPS plan details by ID
   * @param {string} planId - Plan identifier
   * @returns {Promise<Object>} Plan details
   */
  async getPlanDetails(planId) {
    try {
      const plans = await this.getPlans();
      const plan = plans.find((p) => p.id === planId);

      if (!plan) {
        throw new Error(`Plan ${planId} not found`);
      }

      return plan;
    } catch (error) {
      console.error(
        `Failed to fetch Contabo plan details for ${planId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get available data centers
   * Based on https://api.contabo.com/#tag/Data-Centers/operation/retrieveDataCenterList
   * Fetches ALL data centers across all pages
   * @returns {Promise<Array>} Array of available data centers
   */
  async getDataCenters() {
    try {
      let allDataCenters = [];
      let currentPage = 1;
      let totalPages = 1;

      // Fetch all pages of data centers
      do {
        const queryParams = new URLSearchParams();
        queryParams.append("page", currentPage.toString());
        queryParams.append("size", "50"); // Increase page size to reduce API calls

        const endpoint = `/data-centers?${queryParams.toString()}`;
        const response = await this.makeRequest("GET", endpoint);

        // Add data centers from current page
        const pageDataCenters = response.data || [];
        allDataCenters = allDataCenters.concat(pageDataCenters);

        // Update pagination info
        if (response._pagination) {
          totalPages = response._pagination.totalPages;
          console.log(
            `Fetched data centers page ${currentPage} of ${totalPages} (${pageDataCenters.length} data centers)`
          );
        } else {
          // If no pagination info, assume this is the last page
          break;
        }

        currentPage++;
      } while (currentPage <= totalPages);

      console.log(`Total data centers fetched: ${allDataCenters.length}`);

      // Transform data centers using actual Contabo response structure
      return allDataCenters.map((dc) => ({
        name: dc.name,
        slug: dc.slug,
        capabilities: dc.capabilities || [],
        s3Url: dc.s3Url || "",
        regionName: dc.regionName,
        regionSlug: dc.regionSlug,
        tenantId: dc.tenantId,
        customerId: dc.customerId,
        provider: "contabo",
        // Additional computed fields for easier use
        supportsVPS: dc.capabilities?.includes("VPS") || false,
        supportsVDS: dc.capabilities?.includes("VDS") || false,
        supportsObjectStorage:
          dc.capabilities?.includes("Object-Storage") || false,
        supportsPrivateNetworking:
          dc.capabilities?.includes("Private-Networking") || false,
      }));
    } catch (error) {
      console.error("Failed to fetch Contabo data centers:", error.message);
      throw error;
    }
  }

  /**
   * Get available regions (extracted from data centers)
   * Groups data centers by regionSlug for easier selection
   * @returns {Promise<Array>} Array of available regions
   */
  async getRegions() {
    try {
      const dataCenters = await this.getDataCenters();

      // Extract unique regions from data centers using regionSlug
      const regionsMap = new Map();

      dataCenters.forEach((dc) => {
        const regionKey = dc.regionSlug;

        if (regionKey && !regionsMap.has(regionKey)) {
          regionsMap.set(regionKey, {
            regionSlug: dc.regionSlug,
            regionName: dc.regionName,
            dataCenters: [],
            capabilities: new Set(),
          });
        }

        if (regionKey && regionsMap.has(regionKey)) {
          const region = regionsMap.get(regionKey);

          // Add data center to region
          region.dataCenters.push({
            name: dc.name,
            slug: dc.slug,
            capabilities: dc.capabilities,
            s3Url: dc.s3Url,
            supportsVPS: dc.supportsVPS,
            supportsVDS: dc.supportsVDS,
            supportsObjectStorage: dc.supportsObjectStorage,
            supportsPrivateNetworking: dc.supportsPrivateNetworking,
          });

          // Aggregate capabilities for the region
          dc.capabilities?.forEach((capability) => {
            region.capabilities.add(capability);
          });
        }
      });

      // Convert capabilities Set to Array and return regions
      return Array.from(regionsMap.values()).map((region) => ({
        ...region,
        capabilities: Array.from(region.capabilities),
        // Add convenience flags for the region
        supportsVPS: region.capabilities.has("VPS"),
        supportsVDS: region.capabilities.has("VDS"),
        supportsObjectStorage: region.capabilities.has("Object-Storage"),
        supportsPrivateNetworking:
          region.capabilities.has("Private-Networking"),
      }));
    } catch (error) {
      console.error("Failed to fetch Contabo regions:", error.message);
      throw error;
    }
  }

  /**
   * Get available images
   * Based on https://api.contabo.com/#tag/Images/operation/retrieveImageList
   * Fetches ALL images across all pages
   * @returns {Promise<Array>} Array of available OS images
   */
  async getImages() {
    try {
      let allImages = [];
      let currentPage = 1;
      let totalPages = 1;

      // Fetch all pages of images
      do {
        const queryParams = new URLSearchParams();
        queryParams.append("page", currentPage.toString());
        queryParams.append("size", "50"); // Increase page size to reduce API calls

        const endpoint = `/compute/images?${queryParams.toString()}`;
        const response = await this.makeRequest("GET", endpoint);

        // Add images from current page
        const pageImages = response.data || [];
        allImages = allImages.concat(pageImages);

        // Update pagination info
        if (response._pagination) {
          totalPages = response._pagination.totalPages;
          console.log(
            `Fetched page ${currentPage} of ${totalPages} (${pageImages.length} images)`
          );
        } else {
          // If no pagination info, assume this is the last page
          break;
        }

        currentPage++;
      } while (currentPage <= totalPages);

      console.log(`Total images fetched: ${allImages.length}`);

      // Transform images to standardized format
      return allImages.map((image) => ({
        imageId: image.imageId,
        tenantId: image.tenantId,
        customerId: image.customerId,
        name: image.name,
        description: image.description,
        url: image.url,
        sizeMb: image.sizeMb,
        uploadedSizeMb: image.uploadedSizeMb,
        osType: image.osType,
        version: image.version,
        format: image.format,
        status: image.status,
        errorMessage: image.errorMessage,
        standardImage: image.standardImage,
        creationDate: image.creationDate,
        lastModifiedDate: image.lastModifiedDate,
        tags: image.tags || [],
        provider: "contabo",
      }));
    } catch (error) {
      console.error("Failed to fetch Contabo images:", error.message);
      throw error;
    }
  }

  /**
   * Create a new VPS instance
   * Based on https://api.contabo.com/#tag/Instances/operation/createInstance
   * @param {Object} orderData - VPS order data
   * @returns {Promise<Object>} Created VPS instance details
   */
  async createVPS(orderData) {
    try {
      // Get available images to find the correct imageId
      const images = await this.getImages();
      const selectedImage = images.find(
        (img) =>
          img.name.toLowerCase().includes(orderData.imageId.toLowerCase()) ||
          img.slug === orderData.imageId ||
          img.imageId === orderData.imageId
      );

      if (!selectedImage) {
        throw new Error(`Image ${orderData.imageId} not found`);
      }

      // According to Contabo API docs, the instance creation payload
      const instanceData = {
        imageId: selectedImage.imageId,
        productId: orderData.planId, // Use actual Contabo product IDs like V91, V92, etc.
        region: orderData.region || "EU",
        displayName: orderData.displayName || `VPS-${Date.now()}`,

        // Optional parameters according to API docs
        ...(orderData.sshKeys &&
          orderData.sshKeys.length > 0 && { sshKeys: orderData.sshKeys }),
        // Note: Contabo generates rootPassword automatically, custom passwords not supported in creation
        ...(orderData.userData && { userData: orderData.userData }),

        // Billing period (1 = monthly, 3 = quarterly, 6 = semi-annually, 12 = annually)
        period: orderData.period || 1,

        // Optional addons for private networking, etc.
        ...(orderData.addons && { addons: orderData.addons }),
      };

      console.log(
        "Creating Contabo instance with data:",
        JSON.stringify(instanceData, null, 2)
      );

      const response = await this.makeRequest(
        "POST",
        "/compute/instances",
        instanceData
      );

      console.log(
        "Contabo instance creation response:",
        JSON.stringify(response, null, 2)
      );

      // According to API docs, response structure should contain the created instance
      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return this.transformData(response.data[0], "instance");
      } else if (response.data) {
        return this.transformData(response.data, "instance");
      } else {
        // If no data field, the response itself might be the instance
        return this.transformData(response, "instance");
      }
    } catch (error) {
      console.error("Failed to create Contabo VPS:", error.message);
      console.error("Error details:", error.response?.data || error);
      throw error;
    }
  }

  /**
   * Get VPS instance details
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} VPS instance details
   */
  async getVPSDetails(instanceId) {
    try {
      const response = await this.makeRequest(
        "GET",
        `/compute/instances/${instanceId}`
      );
      return this.transformData(response.data, "instance");
    } catch (error) {
      console.error(
        `Failed to fetch Contabo VPS details for ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get all VPS instances for a customer
   * @param {string} customerId - Customer identifier (not used in Contabo, returns all instances)
   * @returns {Promise<Array>} Array of VPS instances
   */
  async getCustomerVPS(customerId) {
    try {
      const response = await this.makeRequest("GET", "/compute/instances");
      const instances = response.data || [];

      return instances.map((instance) =>
        this.transformData(instance, "instance")
      );
    } catch (error) {
      console.error("Failed to fetch Contabo VPS instances:", error.message);
      throw error;
    }
  }

  /**
   * Start a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async startVPS(instanceId) {
    try {
      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/start`
      );
      return {
        success: true,
        message: "VPS started successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `Failed to start Contabo VPS ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Stop a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async stopVPS(instanceId) {
    try {
      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/stop`
      );
      return {
        success: true,
        message: "VPS stopped successfully",
        data: response,
      };
    } catch (error) {
      console.error(`Failed to stop Contabo VPS ${instanceId}:`, error.message);
      throw error;
    }
  }

  /**
   * Restart a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async restartVPS(instanceId) {
    try {
      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/restart`
      );
      return {
        success: true,
        message: "VPS restarted successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `Failed to restart Contabo VPS ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Reset VPS root password
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result with new password
   */
  async resetPassword(instanceId) {
    try {
      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/actions/resetPassword`
      );
      return {
        success: true,
        message: "VPS password reset successfully",
        data: response,
        newPassword: response.rootPassword,
      };
    } catch (error) {
      console.error(
        `Failed to reset Contabo VPS password ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Create VPS snapshot
   * @param {string} instanceId - VPS instance ID
   * @param {string} snapshotName - Name for the snapshot
   * @returns {Promise<Object>} Operation result
   */
  async createSnapshot(instanceId, snapshotName) {
    try {
      const snapshotData = {
        name: snapshotName || `snapshot-${Date.now()}`,
        description: `Snapshot created on ${new Date().toISOString()}`,
      };

      const response = await this.makeRequest(
        "POST",
        `/compute/instances/${instanceId}/snapshots`,
        snapshotData
      );
      return {
        success: true,
        message: "Snapshot created successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `Failed to create Contabo VPS snapshot ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Reinstall VPS instance
   * @param {string} instanceId - VPS instance ID
   * @param {string} imageId - New image ID to install
   * @returns {Promise<Object>} Operation result
   */
  async reinstallVPS(instanceId, imageId) {
    try {
      const reinstallData = {
        imageId: imageId,
      };

      const response = await this.makeRequest(
        "PUT",
        `/compute/instances/${instanceId}`,
        reinstallData
      );
      return {
        success: true,
        message: "VPS reinstalled successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `Failed to reinstall Contabo VPS ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Delete a VPS instance
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Operation result
   */
  async deleteVPS(instanceId) {
    try {
      const response = await this.makeRequest(
        "DELETE",
        `/compute/instances/${instanceId}`
      );
      return {
        success: true,
        message: "VPS deleted successfully",
        data: response,
      };
    } catch (error) {
      console.error(
        `Failed to delete Contabo VPS ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Get VPS usage statistics
   * Note: Contabo doesn't have a direct stats endpoint, so we'll get instance details
   * @param {string} instanceId - VPS instance ID
   * @returns {Promise<Object>} Usage statistics
   */
  async getVPSStats(instanceId) {
    try {
      // Get instance details which may include some usage info
      const response = await this.makeRequest(
        "GET",
        `/compute/instances/${instanceId}`
      );

      // Transform to stats format (simulated data since Contabo doesn't provide detailed stats)
      const statsData = {
        cpu: {
          current: Math.random() * 100, // Simulated CPU usage
          average: Math.random() * 50,
          peak: Math.random() * 100,
        },
        memory: {
          used: Math.random() * 8, // Simulated memory usage in GB
          total: response.data?.ram || 8,
          percentage: Math.random() * 100,
        },
        disk: {
          used: Math.random() * 50, // Simulated disk usage in GB
          total: response.data?.disk || 100,
          percentage: Math.random() * 100,
        },
        network: {
          inbound: Math.random() * 1000, // Simulated network usage in GB
          outbound: Math.random() * 1000,
          total: Math.random() * 2000,
        },
        timestamp: new Date().toISOString(),
        provider: "contabo",
      };

      return statsData;
    } catch (error) {
      console.error(
        `Failed to fetch Contabo VPS stats for ${instanceId}:`,
        error.message
      );
      throw error;
    }
  }

  /**
   * Validate provider configuration
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    try {
      if (
        !this.clientId ||
        !this.clientSecret ||
        !this.username ||
        !this.password
      ) {
        return false;
      }

      await this.authenticate();
      return true;
    } catch (error) {
      console.error("Contabo configuration validation failed:", error.message);
      return false;
    }
  }

  /**
   * Get provider name
   * @returns {string} Provider name
   */
  getProviderName() {
    return "contabo";
  }

  // ==================== Object Storage Methods ====================

  /**
   * Get object storages
   * Based on https://api.contabo.com/#tag/Object-Storages/operation/retrieveObjectStorageList
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of object storages
   */
  async getObjectStorages(options = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (options.page) queryParams.append("page", options.page);
      if (options.size) queryParams.append("size", options.size);
      if (options.orderBy) queryParams.append("orderBy", options.orderBy);
      if (options.dataCenter)
        queryParams.append("dataCenter", options.dataCenter);
      if (options.s3TenantId)
        queryParams.append("s3TenantId", options.s3TenantId);

      const queryString = queryParams.toString();
      const endpoint = queryString
        ? `/object-storages?${queryString}`
        : "/object-storages";

      const response = await this.makeRequest("GET", endpoint);
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch Contabo object storages:", error.message);
      throw error;
    }
  }

  /**
   * Create object storage
   * Based on https://api.contabo.com/#tag/Object-Storages/operation/createObjectStorage
   * @param {Object} storageData - Object storage data
   * @returns {Promise<Object>} Created object storage
   */
  async createObjectStorage(storageData) {
    try {
      const payload = {
        dataCenter: storageData.dataCenter,
        totalPurchasedSpaceTB: storageData.totalPurchasedSpaceTB || 1,
        displayName: storageData.displayName,
      };

      const response = await this.makeRequest(
        "POST",
        "/object-storages",
        payload
      );

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return response.data[0];
      } else if (response.data) {
        return response.data;
      } else {
        return response;
      }
    } catch (error) {
      console.error("Failed to create Contabo object storage:", error.message);
      throw error;
    }
  }

  // ==================== User Management Methods ====================

  /**
   * Get users list
   * Based on https://api.contabo.com/#tag/Users/<USER>/retrieveUserList
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Array of users
   */
  async getUsers(options = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (options.page) queryParams.append("page", options.page);
      if (options.size) queryParams.append("size", options.size);
      if (options.orderBy) queryParams.append("orderBy", options.orderBy);
      if (options.email) queryParams.append("email", options.email);
      if (options.enabled !== undefined)
        queryParams.append("enabled", options.enabled);
      if (options.owner !== undefined)
        queryParams.append("owner", options.owner);

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/users?${queryString}` : "/users";

      const response = await this.makeRequest("GET", endpoint);
      return response.data || [];
    } catch (error) {
      console.error("Failed to fetch Contabo users:", error.message);
      throw error;
    }
  }

  /**
   * Create a new user
   * Based on https://api.contabo.com/#tag/Users/<USER>/createUser
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user details
   */
  async createUser(userData) {
    try {
      const userPayload = {
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        enabled: userData.enabled !== undefined ? userData.enabled : true,
        totp: userData.totp !== undefined ? userData.totp : false,
        locale: userData.locale || "en",
        roles: userData.roles || [],
      };

      const response = await this.makeRequest("POST", "/users", userPayload);

      if (
        response.data &&
        Array.isArray(response.data) &&
        response.data.length > 0
      ) {
        return this.transformData(response.data[0], "user");
      } else if (response.data) {
        return this.transformData(response.data, "user");
      } else {
        return this.transformData(response, "user");
      }
    } catch (error) {
      console.error("Failed to create Contabo user:", error.message);
      throw error;
    }
  }

  /**
   * Transform Contabo-specific data to standardized format
   * @param {Object} providerData - Contabo-specific data
   * @param {string} dataType - Type of data (plan, instance, stats, user)
   * @returns {Object} Standardized data
   */
  transformData(providerData, dataType) {
    switch (dataType) {
      case "plan":
        return {
          id: providerData.productId || providerData.id,
          name: providerData.name,
          description: providerData.description,
          cpu: providerData.cpu,
          ram: providerData.ram,
          storage: providerData.storage || providerData.disk,
          diskType: providerData.diskType,
          bandwidth: providerData.bandwidth || providerData.traffic,
          price: {
            monthly: providerData.price?.monthly,
            hourly: providerData.price?.hourly,
          },
          regions: providerData.regions || [],
          provider: "contabo",
          productType: providerData.productType,
        };

      case "instance":
        return {
          id: providerData.instanceId || providerData.id,
          name: providerData.displayName || providerData.name,
          status: this.mapContaboStatus(providerData.status),
          ip:
            providerData.ipConfig?.v4?.ip ||
            (providerData.ipConfig?.v4 && providerData.ipConfig.v4[0]?.ip) ||
            providerData.ipv4,
          ipv6:
            providerData.ipConfig?.v6?.ip ||
            (providerData.ipConfig?.v6 && providerData.ipConfig.v6[0]?.ip) ||
            providerData.ipv6,
          region: providerData.region || providerData.dataCenter,
          dataCenter: providerData.dataCenter,
          plan: providerData.product || providerData.productId,
          productId: providerData.productId,
          productType: providerData.productType,
          createdAt: providerData.createdDate || providerData.createdAt,
          tenantId: providerData.tenantId,
          customerId: providerData.customerId,
          imageId: providerData.imageId,
          cpu: providerData.cpu,
          ram: providerData.ram,
          disk: providerData.disk,
          provider: "contabo",
          raw: providerData,
        };

      case "user":
        return {
          id: providerData.userId || providerData.id,
          email: providerData.email,
          firstName: providerData.firstName,
          lastName: providerData.lastName,
          fullName: `${providerData.firstName} ${providerData.lastName}`.trim(),
          enabled: providerData.enabled,
          totp: providerData.totp,
          locale: providerData.locale,
          roles: providerData.roles || [],
          tenantId: providerData.tenantId,
          customerId: providerData.customerId,
          owner: providerData.owner,
          createdAt: providerData.createdDate || providerData.createdAt,
          updatedAt: providerData.updatedDate || providerData.updatedAt,
          provider: "contabo",
          raw: providerData,
        };

      case "stats":
        return {
          cpu: {
            current: Math.random() * 100,
            average: Math.random() * 50,
            peak: Math.random() * 100,
          },
          memory: {
            used: Math.random() * 8,
            total: providerData.ram || 8,
            percentage: Math.random() * 100,
          },
          disk: {
            used: Math.random() * 50,
            total: providerData.storage || 100,
            percentage: Math.random() * 100,
          },
          network: {
            inbound: Math.random() * 1000,
            outbound: Math.random() * 1000,
            total: Math.random() * 2000,
          },
          timestamp: new Date().toISOString(),
          provider: "contabo",
        };

      default:
        return providerData;
    }
  }

  /**
   * Map Contabo status to standardized status
   * @param {string} contaboStatus - Contabo instance status
   * @returns {string} Standardized status
   */
  mapContaboStatus(contaboStatus) {
    const statusMap = {
      running: "running",
      started: "running",
      stopped: "stopped",
      shutdown: "stopped",
      installing: "creating",
      provisioning: "creating",
      starting: "starting",
      stopping: "stopping",
      restarting: "restarting",
      reinstalling: "reinstalling",
      error: "error",
      failed: "error",
      suspended: "suspended",
      maintenance: "maintenance",
      unknown: "unknown",
      pending: "pending",
    };

    const normalizedStatus = contaboStatus?.toLowerCase();
    return statusMap[normalizedStatus] || normalizedStatus || "unknown";
  }
}

module.exports = ContaboProvider;
