"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/vps/configure/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx":
/*!***********************************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ConfigureVPSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/vpsService */ \"(app-pages-browser)/./src/app/services/vpsService.js\");\n/* harmony import */ var _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/services/cartService */ \"(app-pages-browser)/./src/app/services/cartService.js\");\n/* harmony import */ var _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/packageService */ \"(app-pages-browser)/./src/app/services/packageService.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/context/AuthContext */ \"(app-pages-browser)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftIcon,CheckIcon,ClockIcon,CpuIcon,EyeIcon,EyeOffIcon,GlobeIcon,HardDriveIcon,KeyIcon,MonitorIcon,RefreshCwIcon,ServerIcon,ShieldIcon,TerminalIcon,UserIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Modern OS Icons Components\nconst UbuntuIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm3.279 18.279c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm-6.558 0c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2zm3.279-6.558c-.553.553-1.447.553-2 0s-.553-1.447 0-2 1.447-.553 2 0 .553 1.447 0 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 34,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 31,\n        columnNumber: 3\n    }, undefined);\n};\n_c = UbuntuIcon;\nconst CentOSIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-purple-600 to-purple-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2L2 7v10l10 5 10-5V7l-10-5zm0 2.5L19.5 8.5v7L12 19.5l-7.5-4v-7L12 4.5z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 48,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 45,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = CentOSIcon;\nconst DebianIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = DebianIcon;\nconst WindowsIcon = (param)=>{\n    let { className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(className, \" bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-3/4 h-3/4 text-white\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3 12V6.75l6-1.32v6.48L3 12zm17-9v8.75l-10 .15V5.21L20 3zM3 13l6 .09v6.81l-6-1.15V13zm17 .25V22l-10-1.91V13.1l10 .15z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 76,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined);\n};\n_c3 = WindowsIcon;\nfunction ConfigureVPSPage() {\n    var _operatingSystems_find, _locations_find;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"vps_configure\");\n    const { setCartCount } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // State management\n    const [vpsPlans, setVpsPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [osImages, setOsImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [regions, setRegions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [orderLoading, setOrderLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize plan from URL params\n    const planId = searchParams.get(\"plan\");\n    const autoBackup = searchParams.get(\"autobackup\") === \"true\";\n    const [selectedPlan, setSelectedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAutoBackup, setIsAutoBackup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    // Function to parse specifications from database\n    const parseSpecifications = (specifications, description)=>{\n        let cores = 0, ram = \"0 GB\", storage = \"0 GB\", traffic = \"32 TB\";\n        // Parse from specifications array\n        if (specifications && Array.isArray(specifications)) {\n            specifications.forEach((spec)=>{\n                const value = spec.value || \"\";\n                const lowerValue = value.toLowerCase();\n                // Parse CPU cores\n                if (lowerValue.includes(\"cpu\") || lowerValue.includes(\"core\") || lowerValue.includes(\"vcpu\")) {\n                    const cpuMatch = value.match(/(\\d+)/);\n                    if (cpuMatch) cores = parseInt(cpuMatch[1]);\n                }\n                // Parse RAM\n                if (lowerValue.includes(\"ram\") || lowerValue.includes(\"memory\") || lowerValue.includes(\"gb ram\")) {\n                    const ramMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n                }\n                // Parse Storage\n                if (lowerValue.includes(\"storage\") || lowerValue.includes(\"disk\") || lowerValue.includes(\"ssd\") || lowerValue.includes(\"nvme\")) {\n                    const storageMatch = value.match(/(\\d+)\\s*gb/i);\n                    if (storageMatch) {\n                        const storageType = lowerValue.includes(\"nvme\") ? \"NVMe\" : lowerValue.includes(\"ssd\") ? \"SSD\" : \"\";\n                        storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n                    }\n                }\n                // Parse Traffic/Bandwidth\n                if (lowerValue.includes(\"traffic\") || lowerValue.includes(\"bandwidth\") || lowerValue.includes(\"transfer\")) {\n                    const trafficMatch = value.match(/(\\d+)\\s*(tb|gb)/i);\n                    if (trafficMatch) {\n                        traffic = \"\".concat(trafficMatch[1], \" \").concat(trafficMatch[2].toUpperCase());\n                    }\n                }\n            });\n        }\n        // Fallback: parse from description if specifications are empty\n        if (cores === 0 && description) {\n            const descLower = description.toLowerCase();\n            const cpuMatch = description.match(/(\\d+)\\s*(cpu|core|vcpu)/i);\n            if (cpuMatch) cores = parseInt(cpuMatch[1]);\n            const ramMatch = description.match(/(\\d+)\\s*gb\\s*ram/i);\n            if (ramMatch) ram = \"\".concat(ramMatch[1], \" GB\");\n            const storageMatch = description.match(/(\\d+)\\s*gb\\s*(storage|disk|ssd|nvme)/i);\n            if (storageMatch) {\n                const storageType = descLower.includes(\"nvme\") ? \"NVMe\" : descLower.includes(\"ssd\") ? \"SSD\" : \"\";\n                storage = \"\".concat(storageMatch[1], \" GB \").concat(storageType).trim();\n            }\n        }\n        return {\n            cores,\n            ram,\n            storage,\n            traffic\n        };\n    };\n    // Fetch VPS packages and find the selected one\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchVPSPackages = async ()=>{\n            try {\n                setLoading(true);\n                // Récupérer les packages VPS depuis la base de données\n                const response = await _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPackages(\"VPS Hosting\");\n                console.log(\"VPS packages response:\", response);\n                let vpsPackages = [];\n                if (response.data && Array.isArray(response.data)) {\n                    vpsPackages = response.data;\n                } else if (response.data && response.data.packages && Array.isArray(response.data.packages)) {\n                    vpsPackages = response.data.packages;\n                } else if (Array.isArray(response)) {\n                    vpsPackages = response;\n                }\n                // Transformer les packages de la base de données\n                const transformedPlans = vpsPackages.map((pkg)=>{\n                    const specs = parseSpecifications(pkg.specifications, pkg.description);\n                    return {\n                        id: pkg._id,\n                        _id: pkg._id,\n                        name: pkg.name,\n                        price: pkg.price,\n                        cores: specs.cores,\n                        ram: specs.ram,\n                        storage: specs.storage,\n                        traffic: specs.traffic,\n                        description: pkg.description,\n                        specifications: pkg.specifications\n                    };\n                });\n                setVpsPlans(transformedPlans);\n                // Trouver le package sélectionné par son ID\n                if (planId && transformedPlans.length > 0) {\n                    const foundPlan = transformedPlans.find((plan)=>{\n                        var _plan__id, _plan_id;\n                        return plan._id === planId || plan.id === planId || ((_plan__id = plan._id) === null || _plan__id === void 0 ? void 0 : _plan__id.toString()) === planId || ((_plan_id = plan.id) === null || _plan_id === void 0 ? void 0 : _plan_id.toString()) === planId;\n                    });\n                    if (foundPlan) {\n                        setSelectedPlan(foundPlan);\n                        console.log(\"Selected plan found:\", foundPlan);\n                    } else {\n                        console.error(\"Plan not found with ID:\", planId);\n                        console.log(\"Available plans:\", transformedPlans.map((p)=>({\n                                id: p.id,\n                                _id: p._id,\n                                name: p.name\n                            })));\n                        setError(\"Package VPS non trouv\\xe9\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Error fetching VPS plans:\", error);\n                setError(\"Erreur lors du chargement des plans VPS\");\n                setVpsPlans([]);\n                setSelectedPlan(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        // Fetch dynamic OS images from API\n        const fetchOSImages = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching OS images from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                console.log(\"✅ OS Images response:\", response);\n                let images = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    images = response.data.data;\n                } else if (response.data && response.data.images && Array.isArray(response.data.images)) {\n                    images = response.data.images;\n                }\n                // Transform API data to expected format\n                const transformedImages = images.map((img)=>({\n                        id: img.imageId || img.id,\n                        name: img.name,\n                        description: img.description,\n                        type: img.osType || \"linux\",\n                        version: img.version,\n                        provider: img.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting OS images:\", transformedImages.length, \"images\");\n                setOsImages(transformedImages);\n                // Set default OS if available\n                if (transformedImages.length > 0) {\n                    const defaultOS = transformedImages.find((img)=>img.name.toLowerCase().includes(\"ubuntu\") && img.name.toLowerCase().includes(\"22.04\")) || transformedImages[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default OS:\", defaultOS.name, defaultOS.id);\n                    setSelectedOS(defaultOS.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching OS images:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticOsImages = [\n                    {\n                        id: \"ubuntu-20.04\",\n                        name: \"Ubuntu 20.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"ubuntu-22.04\",\n                        name: \"Ubuntu 22.04 LTS\",\n                        type: \"ubuntu\"\n                    },\n                    {\n                        id: \"centos-7\",\n                        name: \"CentOS 7\",\n                        type: \"centos\"\n                    },\n                    {\n                        id: \"debian-11\",\n                        name: \"Debian 11\",\n                        type: \"debian\"\n                    }\n                ];\n                setOsImages(staticOsImages);\n                setSelectedOS(\"ubuntu-22.04\");\n            }\n        };\n        // Fetch dynamic regions from API\n        const fetchRegions = async ()=>{\n            try {\n                console.log(\"\\uD83D\\uDD0D Fetching regions from API...\");\n                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                console.log(\"✅ Regions response:\", response);\n                let regions = [];\n                if (response.data.data && Array.isArray(response.data.data)) {\n                    regions = response.data.data;\n                } else if (response.data && response.data.regions && Array.isArray(response.data.regions)) {\n                    regions = response.data.regions;\n                }\n                // Transform API data to expected format\n                const transformedRegions = regions.map((region)=>({\n                        id: region.regionSlug,\n                        name: region.regionName,\n                        provider: region.provider\n                    }));\n                console.log(\"\\uD83D\\uDD04 Setting regions:\", transformedRegions.length, \"regions\");\n                setRegions(transformedRegions);\n                // Set default region if available\n                if (transformedRegions.length > 0) {\n                    const defaultRegion = transformedRegions.find((region)=>region.id === \"EU\") || transformedRegions[0];\n                    console.log(\"\\uD83D\\uDD04 Setting default region:\", defaultRegion.name, defaultRegion.id);\n                    setSelectedLocation(defaultRegion.id);\n                }\n            } catch (error) {\n                var _error_response;\n                console.error(\"❌ Error fetching regions:\", error);\n                console.error(\"Error details:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data) || error.message);\n                // Fallback to static data if API fails\n                const staticRegions = [\n                    {\n                        id: \"EU\",\n                        name: \"European Union\",\n                        description: \"Germany\",\n                        country: \"Germany\",\n                        city: \"Nuremberg\"\n                    },\n                    {\n                        id: \"US-central\",\n                        name: \"United States Central\",\n                        description: \"St. Louis\",\n                        country: \"United States\",\n                        city: \"St. Louis\"\n                    },\n                    {\n                        id: \"SG\",\n                        name: \"Asia Pacific\",\n                        description: \"Singapore\",\n                        country: \"Singapore\",\n                        city: \"Singapore\"\n                    }\n                ];\n                setRegions(staticRegions);\n                setSelectedLocation(\"EU\");\n            }\n        };\n        // Appeler les fonctions pour récupérer les données\n        fetchVPSPackages();\n        fetchOSImages();\n        fetchRegions();\n    }, [\n        planId\n    ]);\n    // Configuration state\n    const [selectedOS, setSelectedOS] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ubuntu-20.04\");\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"france\");\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"monthly\");\n    const [additionalIPs, setAdditionalIPs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [backupEnabled, setBackupEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(autoBackup);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // New Contabo-style options\n    const [autoBackupOption, setAutoBackupOption] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [privateNetworking, setPrivateNetworking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ipv4Addresses, setIpv4Addresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [objectStorage, setObjectStorage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [serverManagement, setServerManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"unmanaged\");\n    const [monitoring, setMonitoring] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [ssl, setSsl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    // Login & Password section state\n    const [serverUsername, setServerUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"root\");\n    const [serverPassword, setServerPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Generate secure password function\n    const generateSecurePassword = ()=>{\n        const length = 16;\n        const charset = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*\";\n        let password = \"\";\n        for(let i = 0; i < length; i++){\n            password += charset.charAt(Math.floor(Math.random() * charset.length));\n        }\n        setServerPassword(password);\n        setPasswordError(\"\");\n    };\n    // Validate password\n    const validatePassword = (password)=>{\n        if (!password) {\n            return \"Please enter a valid password\";\n        }\n        if (password.length < 8) {\n            return \"Password must be at least 8 characters long\";\n        }\n        return \"\";\n    };\n    // Handle adding VPS to cart\n    const handleAddToCart = async ()=>{\n        if (!selectedPlan) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez s\\xe9lectionner un plan VPS\");\n            return;\n        }\n        try {\n            var _selectedPlan_vpsConfig, _response_data_cart, _response_data;\n            setOrderLoading(true);\n            // Vérifier si nous avons un ID valide\n            const packageId = selectedPlan._id || selectedPlan.id;\n            if (!packageId) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"ID du package VPS manquant. Veuillez r\\xe9essayer.\");\n                return;\n            }\n            // Map frontend selections to Contabo API format\n            const contaboRegionMap = {\n                france: \"EU\",\n                EU: \"EU\",\n                germany: \"EU\",\n                \"US-central\": \"US-east\",\n                usa: \"US-east\",\n                SG: \"SIN\",\n                singapore: \"SIN\",\n                asia: \"SIN\"\n            };\n            const contaboOSMap = {\n                \"ubuntu-20.04\": \"ubuntu-20.04\",\n                \"ubuntu-22.04\": \"ubuntu-22.04\",\n                \"ubuntu-24.04\": \"ubuntu-24.04\",\n                \"centos-7\": \"centos-7\",\n                \"centos-8\": \"centos-8\",\n                \"debian-10\": \"debian-10\",\n                \"debian-11\": \"debian-11\",\n                \"windows-2019\": \"windows-server-2019\",\n                \"windows-2022\": \"windows-server-2022\"\n            };\n            // Generate display name if not provided\n            const displayName = \"\".concat(selectedPlan.name, \"-\").concat(Date.now());\n            // Préparer les données pour l'ajout au panier avec configuration Contabo\n            const cartData = {\n                packageId: packageId,\n                quantity: quantity,\n                period: selectedPeriod === \"monthly\" ? 1 : selectedPeriod === \"6months\" ? 6 : 12,\n                // Configuration personnalisée pour Contabo VPS\n                customConfiguration: {\n                    // Contabo API fields\n                    planId: ((_selectedPlan_vpsConfig = selectedPlan.vpsConfig) === null || _selectedPlan_vpsConfig === void 0 ? void 0 : _selectedPlan_vpsConfig.providerProductId) || selectedPlan.id,\n                    provider: \"contabo\",\n                    region: contaboRegionMap[selectedLocation] || \"EU\",\n                    operatingSystem: contaboOSMap[selectedOS] || selectedOS,\n                    displayName: displayName,\n                    sshKeys: [],\n                    userData: \"\",\n                    addons: {\n                        privatenetworking: privateNetworking !== \"none\",\n                        autobackup: autoBackupOption !== \"none\",\n                        monitoring: monitoring !== \"none\"\n                    },\n                    // Plan specifications for reference\n                    cpu: selectedPlan.cores || selectedPlan.cpu,\n                    ram: selectedPlan.ram,\n                    storage: selectedPlan.storage,\n                    bandwidth: selectedPlan.traffic || selectedPlan.bandwidth,\n                    // Frontend-specific fields for display\n                    frontendConfig: {\n                        operatingSystem: selectedOS,\n                        location: selectedLocation,\n                        additionalIPs: additionalIPs,\n                        backup: isAutoBackup || backupEnabled,\n                        planName: selectedPlan.name,\n                        autoBackupOption: autoBackupOption,\n                        privateNetworking: privateNetworking,\n                        ipv4Addresses: ipv4Addresses,\n                        objectStorage: objectStorage,\n                        serverManagement: serverManagement,\n                        monitoring: monitoring,\n                        ssl: ssl\n                    }\n                }\n            };\n            console.log(\"Adding VPS to cart:\", cartData);\n            console.log(\"Selected plan:\", selectedPlan);\n            // Ajouter au panier via le service\n            const response = await _app_services_cartService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].addItemToCart(cartData);\n            console.log(\"Cart response:\", response);\n            // Mettre à jour le compteur du panier\n            if ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_cart = _response_data.cart) === null || _response_data_cart === void 0 ? void 0 : _response_data_cart.cartCount) {\n                setCartCount(response.data.cart.cartCount);\n            }\n            // Afficher le message de succès\n            react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(selectedPlan.name, \" ajout\\xe9 au panier avec succ\\xe8s!\"));\n            // Rediriger vers le panier\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1, _error_response2, _error_response3, _error_response4;\n            console.error(\"Error adding VPS to cart:\", error);\n            console.error(\"Error response:\", error.response);\n            console.error(\"Error data:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // Gestion des erreurs spécifiques\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(error.response.data.message);\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Package VPS non trouv\\xe9. Veuillez contacter le support.\");\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Veuillez vous connecter pour ajouter au panier.\");\n                router.push(\"/auth/login\");\n            } else if (((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status) === 400) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Donn\\xe9es invalides. Veuillez v\\xe9rifier votre s\\xe9lection.\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Erreur lors de l'ajout au panier. Veuillez r\\xe9essayer.\");\n            }\n        } finally{\n            setOrderLoading(false);\n        }\n    };\n    // Use dynamic OS images data with fallback to static data\n    const operatingSystems = osImages.length > 0 ? osImages.map((os)=>({\n            id: os.id,\n            name: os.name,\n            icon: getOSIcon(os.type || os.osType),\n            type: os.type || os.osType || \"linux\",\n            description: os.description,\n            version: os.version,\n            provider: os.provider\n        })) : [\n        {\n            id: \"ubuntu-20.04\",\n            name: \"Ubuntu 20.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"ubuntu-22.04\",\n            name: \"Ubuntu 22.04 LTS\",\n            icon: UbuntuIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"centos-8\",\n            name: \"CentOS 8\",\n            icon: CentOSIcon,\n            type: \"linux\"\n        },\n        {\n            id: \"debian-11\",\n            name: \"Debian 11\",\n            icon: DebianIcon,\n            type: \"linux\"\n        }\n    ];\n    // Helper function to get OS icon based on type\n    function getOSIcon(osType) {\n        const iconMap = {\n            ubuntu: UbuntuIcon,\n            centos: CentOSIcon,\n            debian: DebianIcon,\n            windows: WindowsIcon,\n            linux: UbuntuIcon\n        };\n        // Check if osType contains specific OS names\n        if (osType && typeof osType === \"string\") {\n            const lowerType = osType.toLowerCase();\n            if (lowerType.includes(\"ubuntu\")) return UbuntuIcon;\n            if (lowerType.includes(\"centos\")) return CentOSIcon;\n            if (lowerType.includes(\"debian\")) return DebianIcon;\n            if (lowerType.includes(\"windows\")) return WindowsIcon;\n        }\n        return iconMap[osType] || UbuntuIcon;\n    }\n    // Use dynamic regions data with fallback to static data\n    const locations = regions.length > 0 ? regions.map((region)=>({\n            id: region.id,\n            name: region.name,\n            flag: getRegionFlag(region.country),\n            ping: getRegionPing(region.id),\n            description: region.description,\n            city: region.city,\n            country: region.country\n        })) : [\n        {\n            id: \"EU\",\n            name: \"European Union\",\n            flag: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            ping: \"15ms\",\n            description: \"Germany\",\n            city: \"Nuremberg\",\n            country: \"Germany\"\n        },\n        {\n            id: \"US-central\",\n            name: \"United States Central\",\n            flag: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            ping: \"120ms\",\n            description: \"St. Louis\",\n            city: \"St. Louis\",\n            country: \"United States\"\n        },\n        {\n            id: \"SG\",\n            name: \"Asia Pacific\",\n            flag: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            ping: \"200ms\",\n            description: \"Singapore\",\n            city: \"Singapore\",\n            country: \"Singapore\"\n        }\n    ];\n    // Helper functions for region display\n    function getRegionFlag(country) {\n        const flagMap = {\n            Germany: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            \"United States\": \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            Singapore: \"\\uD83C\\uDDF8\\uD83C\\uDDEC\",\n            France: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            Netherlands: \"\\uD83C\\uDDF3\\uD83C\\uDDF1\"\n        };\n        return flagMap[country] || \"\\uD83C\\uDF0D\";\n    }\n    function getRegionPing(regionId) {\n        const pingMap = {\n            EU: \"15ms\",\n            \"US-central\": \"120ms\",\n            \"US-east\": \"110ms\",\n            \"US-west\": \"130ms\",\n            SG: \"200ms\"\n        };\n        return pingMap[regionId] || \"50ms\";\n    }\n    const calculateTotal = ()=>{\n        if (!selectedPlan) return 0;\n        let total = selectedPlan.price;\n        // Additional IPs cost\n        total += additionalIPs * 15; // 15 MAD per additional IP\n        // Backup cost (only if not auto backup plan)\n        if (!isAutoBackup && backupEnabled) {\n            total += 20; // 20 MAD for backup\n        }\n        // Auto Backup cost (Contabo style)\n        if (autoBackupOption === \"auto\") {\n            total += 18; // €1.79 ≈ 18 MAD per month\n        }\n        // Private Networking cost\n        if (privateNetworking === \"enabled\") {\n            total += 28; // 28 MAD per month\n        }\n        // IPv4 additional addresses cost\n        if (ipv4Addresses === 2) {\n            total += 42; // 42 MAD per additional IP\n        }\n        // Object Storage cost\n        const objectStorageCosts = {\n            \"250gb\": 50,\n            \"500gb\": 95,\n            \"750gb\": 140,\n            \"1tb\": 180\n        };\n        if (objectStorage !== \"none\" && objectStorageCosts[objectStorage]) {\n            total += objectStorageCosts[objectStorage];\n        }\n        // Server Management cost\n        if (serverManagement === \"managed\") {\n            total += 1340; // 1340 MAD per month for managed\n        }\n        // Monitoring cost\n        if (monitoring === \"full\") {\n            total += 140; // 140 MAD per month for full monitoring\n        }\n        // SSL cost (one-time charges, but we'll add monthly equivalent)\n        const sslCosts = {\n            basic: 76,\n            wildcard: 228\n        };\n        if (ssl !== \"none\" && sslCosts[ssl]) {\n            total += sslCosts[ssl];\n        }\n        // Apply quantity\n        total *= quantity;\n        // Period multiplier\n        const multipliers = {\n            monthly: 1,\n            \"6months\": 6 * 0.97,\n            annually: 12 * 0.9\n        };\n        return total * multipliers[selectedPeriod];\n    };\n    // Loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 797,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600\",\n                        children: \"Loading VPS configuration...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 798,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 795,\n            columnNumber: 7\n        }, this);\n    }\n    // Error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-red-600 mb-2\",\n                        children: \"Error loading VPS configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 811,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 814,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>window.location.reload(),\n                        color: \"blue\",\n                        size: \"sm\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 815,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 810,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n            lineNumber: 809,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 sm:gap-4 w-full sm:w-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outlined\",\n                                        size: \"sm\",\n                                        onClick: ()=>window.history.back(),\n                                        className: \"border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 sm:mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: t(\"back\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"min-w-0 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h4\",\n                                                className: \"text-lg sm:text-2xl text-gray-900 font-bold truncate\",\n                                                children: t(\"page_title\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 truncate\",\n                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 833,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-left sm:text-right w-full sm:w-auto flex-shrink-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                        children: t(\"price_from\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        variant: \"h3\",\n                                        className: \"text-lg sm:text-2xl text-blue-600 font-bold\",\n                                        children: [\n                                            (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0,\n                                            \" MAD\",\n                                            t(\"per_month\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 855,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                        lineNumber: 832,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 831,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 830,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6 sm:space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 sm:gap-4 mb-4 sm:mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 sm:w-6 h-5 sm:h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                variant: \"h5\",\n                                                                className: \"text-lg sm:text-xl text-gray-900 font-bold truncate\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isAutoBackup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 890,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                        children: \"Auto Backup Inclus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 891,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 889,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 881,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 901,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"vcpu_cores\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 902,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.cores) || 0\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 900,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 bg-blue-600 rounded mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"ram\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.ram) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 919,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"storage\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 920,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.storage) || \"0 GB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 923,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 918,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center p-2 sm:p-3 bg-gray-50 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-5 sm:w-6 h-5 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 928,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs sm:text-sm text-gray-600\",\n                                                                children: t(\"traffic\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.traffic) || \"0 TB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 899,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 875,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"1. \",\n                                                    t(\"billing_period\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"billing_period_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        id: \"monthly\",\n                                                        label: \"1 mois\",\n                                                        discount: \"\",\n                                                        price: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0\n                                                    },\n                                                    {\n                                                        id: \"6months\",\n                                                        label: \"6 mois\",\n                                                        discount: \"3% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 6 * 0.97)\n                                                    },\n                                                    {\n                                                        id: \"annually\",\n                                                        label: \"12 mois\",\n                                                        discount: \"10% de r\\xe9duction\",\n                                                        price: Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * 12 * 0.9)\n                                                    }\n                                                ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedPeriod(period.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 rounded-full border-2 flex-shrink-0 \".concat(selectedPeriod === period.id ? \"border-blue-600 bg-blue-600\" : \"border-gray-300\"),\n                                                                            children: selectedPeriod === period.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 992,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 984,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                                    children: period.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 996,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                period.discount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs sm:text-sm text-green-600 font-medium\",\n                                                                                    children: period.discount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1000,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 995,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 983,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-right flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-bold text-sm sm:text-base text-gray-900\",\n                                                                            children: [\n                                                                                period.price,\n                                                                                \" MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500\",\n                                                                            children: period.id === \"monthly\" ? \"/mois\" : period.id === \"6months\" ? \"/6 mois\" : \"/an\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1010,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 982,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, period.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 973,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 952,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"2. \",\n                                                    t(\"choose_os\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_os_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this),\n                                            osImages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des syst\\xe8mes d'exploitation...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            operatingSystems.length,\n                                                            \" OS disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching OS images...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getImages(\"contabo\");\n                                                                console.log(\"✅ Force fetch result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-blue-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des OS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        osImages.length,\n                                                        \" syst\\xe8mes d'exploitation charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 1064,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1063,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: operatingSystems.map((os)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedOS(os.id),\n                                                        className: \"relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedOS === os.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3 sm:gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(os.icon, {\n                                                                    className: \"w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1082,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-sm sm:text-base text-gray-900 break-words\",\n                                                                            children: os.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs sm:text-sm text-gray-500 mt-1\",\n                                                                            children: os.type === \"linux\" ? \"Linux Distribution\" : \"Windows Server\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1087,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1083,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedOS === os.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1094,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, os.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1070,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1027,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1026,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: [\n                                                    \"3. \",\n                                                    t(\"choose_location\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1106,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                className: \"text-sm sm:text-base text-gray-600 mb-4\",\n                                                children: t(\"choose_location_desc\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this),\n                                            regions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-gray-500\",\n                                                        children: \"\\uD83D\\uDD04 Chargement des emplacements...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"(\",\n                                                            locations.length,\n                                                            \" emplacements disponibles en fallback)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: async ()=>{\n                                                            console.log(\"\\uD83D\\uDD04 Force fetching regions...\");\n                                                            try {\n                                                                const response = await _app_services_vpsService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getRegions(\"contabo\");\n                                                                console.log(\"✅ Force fetch regions result:\", response);\n                                                            } catch (error) {\n                                                                console.error(\"❌ Force fetch regions error:\", error);\n                                                            }\n                                                        },\n                                                        className: \"mt-2 px-4 py-2 bg-green-500 text-white rounded text-sm\",\n                                                        children: \"\\uD83D\\uDD04 Forcer le chargement des r\\xe9gions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-green-600 text-sm\",\n                                                    children: [\n                                                        \"✅ \",\n                                                        regions.length,\n                                                        \" emplacements charg\\xe9s depuis l'API\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                    lineNumber: 1145,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1144,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4\",\n                                                children: locations.map((location)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedLocation(location.id),\n                                                        className: \"p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 \".concat(selectedLocation === location.id ? \"border-blue-600 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 sm:gap-3 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xl sm:text-2xl flex-shrink-0\",\n                                                                            children: location.id\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1163,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"min-w-0 flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-medium text-sm sm:text-base text-gray-900 truncate\",\n                                                                                children: location.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1167,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1166,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                selectedLocation === location.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 sm:w-5 h-4 sm:h-5 text-blue-600 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1173,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                            lineNumber: 1161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, location.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1152,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"4. Data Protection with Auto Backup\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"auto\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"auto\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"auto\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1209,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1204,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1203,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full inline-block mb-3\",\n                                                                        children: \"Notre Recommandation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1220,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2\",\n                                                                        children: \"Auto Backup\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1223,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-blue-600 font-bold text-lg mb-3\",\n                                                                        children: \"18 MAD/mois\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-600 mb-2 font-medium\",\n                                                                        children: \"Set it and forget it.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1229,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-500 text-sm mb-4\",\n                                                                        children: \"Data security with no effort\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1232,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1238,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"automated\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1239,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1237,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1244,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"daily\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1245,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1243,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1250,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"1-Click Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1251,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1249,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1256,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"10 days\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1259,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1255,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1236,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1219,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setAutoBackupOption(\"none\"),\n                                                        className: \"relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200 \".concat(autoBackupOption === \"none\" ? \"border-blue-500 bg-blue-50 shadow-md\" : \"border-gray-200 hover:border-blue-300 hover:shadow-sm\"),\n                                                        children: [\n                                                            autoBackupOption === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute top-4 right-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-white\",\n                                                                        fill: \"currentColor\",\n                                                                        viewBox: \"0 0 20 20\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            fillRule: \"evenodd\",\n                                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                            clipRule: \"evenodd\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1284,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1279,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1278,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-xl text-gray-900 mb-2 mt-8\",\n                                                                        children: \"None\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1295,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-green-600 font-bold text-lg mb-6\",\n                                                                        children: \"Free\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1298,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-white rounded-lg p-4 space-y-3 text-sm border\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Mode\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1304,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1305,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1303,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Frequency\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1310,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"on demand\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1311,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1309,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Recovery\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1316,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"manual\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1317,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1315,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600\",\n                                                                                        children: \"Backup Retention\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1322,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"font-semibold text-gray-900\",\n                                                                                        children: \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1325,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1321,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1302,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1294,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1268,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"5. Networking\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Private Networking\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: privateNetworking,\n                                                                        onChange: (e)=>setPrivateNetworking(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"No Private Networking\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1356,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"enabled\",\n                                                                                children: \"Private Networking Enabled\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1357,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1351,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: privateNetworking === \"enabled\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"28 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1363,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1367,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1361,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Bandwidth\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                        children: \"32 TB Out + Unlimited In\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1381,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs sm:text-sm text-purple-600\",\n                                                                        children: \"200 Mbit/s Connection\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"IPv4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ipv4Addresses,\n                                                                        onChange: (e)=>setIpv4Addresses(parseInt(e.target.value)),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1,\n                                                                                children: \"1 IP Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1403,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 2,\n                                                                                children: \"1 IP Address + 1 Additional IP\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1404,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[60px] text-right\",\n                                                                        children: ipv4Addresses === 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"42 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1410,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1414,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1408,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1336,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-3 sm:mb-4\",\n                                                children: \"6. Add-Ons\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Object Storage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1438,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: objectStorage,\n                                                                        onChange: (e)=>setObjectStorage(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1447,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"250gb\",\n                                                                                children: \"250 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1448,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"500gb\",\n                                                                                children: \"500 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1449,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"750gb\",\n                                                                                children: \"750 GB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1450,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"1tb\",\n                                                                                children: \"1 TB Object Storage\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1451,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1442,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            objectStorage === \"250gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"50 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1455,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"500gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"95 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1460,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"750gb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"140 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1465,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"1tb\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                children: \"180 MAD\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1470,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            objectStorage === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1475,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1453,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Server Management\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1485,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: serverManagement,\n                                                                        onChange: (e)=>setServerManagement(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"unmanaged\",\n                                                                                children: \"Unmanaged\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1494,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"managed\",\n                                                                                children: \"Managed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1495,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1489,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: serverManagement === \"unmanaged\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1499,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"1340 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1503,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1497,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1488,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1484,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"Monitoring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: monitoring,\n                                                                        onChange: (e)=>setMonitoring(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1522,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"full\",\n                                                                                children: \"Full Monitoring\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1523,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1517,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: monitoring === \"full\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                            children: \"140 MAD\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1527,\n                                                                            columnNumber: 27\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                            children: \"Free\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1531,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1525,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1516,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1512,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-sm sm:text-base text-gray-900\",\n                                                                children: \"SSL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1541,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: ssl,\n                                                                        onChange: (e)=>setSsl(e.target.value),\n                                                                        className: \"border border-gray-300 rounded px-3 py-2 pr-8 min-w-[240px] appearance-none bg-white text-sm sm:text-base\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"none\",\n                                                                                children: \"None\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1550,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"basic\",\n                                                                                children: \"SSL certificate\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1551,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"wildcard\",\n                                                                                children: \"SSL certificate (wildcard)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1552,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1545,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-[80px] text-right\",\n                                                                        children: [\n                                                                            ssl === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"914 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1559,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1562,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1558,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"wildcard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-right\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-sm sm:text-base font-bold text-gray-900\",\n                                                                                        children: \"2740 MAD\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1569,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs sm:text-sm text-gray-500\",\n                                                                                        children: \"One off charge\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1572,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1568,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            ssl === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base font-bold text-green-600\",\n                                                                                children: \"Free\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1578,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1556,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1544,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1540,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1435,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1427,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1426,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 873,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:sticky lg:top-4 lg:max-h-[calc(100vh-2rem)] lg:overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"shadow-lg h-full flex flex-col\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                        className: \"p-4 sm:p-6 flex flex-col h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"text-lg sm:text-xl text-gray-900 font-bold mb-4 sm:mb-6 flex-shrink-0\",\n                                                children: t(\"order_summary\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1595,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 sm:space-y-4 mb-4 sm:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm sm:text-base text-gray-600\",\n                                                                                children: (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.name) || \"Loading...\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1606,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: \"Quantit\\xe9:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1610,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.max(1, quantity - 1)),\n                                                                                                disabled: quantity === 1,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"-\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1614,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"w-8 text-center font-medium text-xs\",\n                                                                                                children: quantity\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1625,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                                size: \"sm\",\n                                                                                                variant: \"outlined\",\n                                                                                                onClick: ()=>setQuantity(Math.min(10, quantity + 1)),\n                                                                                                disabled: quantity === 10,\n                                                                                                className: \"w-6 h-6 p-0 border-gray-300 text-gray-600 hover:bg-gray-50 flex-shrink-0 min-w-0\",\n                                                                                                children: \"+\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                                lineNumber: 1628,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                        lineNumber: 1613,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                lineNumber: 1609,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1605,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            ((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1642,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1604,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            additionalIPs > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"IPs additionnelles (\",\n                                                                            additionalIPs,\n                                                                            \" \\xd7 \",\n                                                                            quantity,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1649,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            additionalIPs * 15 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1652,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1648,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            !isAutoBackup && backupEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base text-gray-600 min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"Sauvegarde automatique \\xd7 \",\n                                                                            quantity\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1660,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            20 * quantity,\n                                                                            \" MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1663,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1659,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedPeriod !== \"monthly\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-green-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base min-w-0 flex-1 pr-2\",\n                                                                        children: [\n                                                                            \"R\\xe9duction (\",\n                                                                            selectedPeriod === \"6months\" ? \"3%\" : \"10%\",\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm sm:text-base flex-shrink-0\",\n                                                                        children: [\n                                                                            \"-\",\n                                                                            Math.round(((selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.price) || 0) * quantity * (selectedPeriod === \"6months\" ? 6 * 0.03 : 12 * 0.1)),\n                                                                            \" \",\n                                                                            \"MAD\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1675,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1670,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                                className: \"border-gray-200\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1689,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start text-base sm:text-lg font-bold\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"min-w-0 flex-1 pr-2\",\n                                                                        children: t(\"total\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1692,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-600 flex-shrink-0 text-right\",\n                                                                        children: [\n                                                                            Math.round(calculateTotal()),\n                                                                            \" MAD/\",\n                                                                            selectedPeriod === \"monthly\" ? \"mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"an\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1695,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1691,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-200\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs sm:text-sm text-gray-600 space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"os_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1709,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_operatingSystems_find = operatingSystems.find((os)=>os.id === selectedOS)) === null || _operatingSystems_find === void 0 ? void 0 : _operatingSystems_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1710,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1708,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: [\n                                                                                        t(\"location_label\"),\n                                                                                        \":\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1719,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: (_locations_find = locations.find((loc)=>loc.id === selectedLocation)) === null || _locations_find === void 0 ? void 0 : _locations_find.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1720,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1718,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                    children: \"P\\xe9riode:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1729,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                \"\\xa0\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"break-all\",\n                                                                                    children: selectedPeriod === \"monthly\" ? \"1 mois\" : selectedPeriod === \"6months\" ? \"6 mois\" : \"12 mois\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                                    lineNumber: 1730,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                            lineNumber: 1728,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                    lineNumber: 1707,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1706,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1603,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        size: \"lg\",\n                                                        className: \"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 mb-4 py-3 sm:py-4 text-sm sm:text-base font-semibold\",\n                                                        onClick: handleAddToCart,\n                                                        disabled: orderLoading || loading || !selectedPlan,\n                                                        children: orderLoading ? \"Ajout en cours...\" : \"Ajouter au panier\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1742,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1753,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Paiement s\\xe9curis\\xe9\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1754,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1752,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftIcon_CheckIcon_ClockIcon_CpuIcon_EyeIcon_EyeOffIcon_GlobeIcon_HardDriveIcon_KeyIcon_MonitorIcon_RefreshCwIcon_ServerIcon_ShieldIcon_TerminalIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-3 sm:w-4 h-3 sm:h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1757,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"D\\xe9ploiement en 5 minutes\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                        lineNumber: 1758,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                                lineNumber: 1756,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                        lineNumber: 1751,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                                lineNumber: 1602,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                        lineNumber: 1594,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                    lineNumber: 1593,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                                lineNumber: 1592,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                            lineNumber: 1591,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                    lineNumber: 871,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n                lineNumber: 870,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\stage PFE\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\configure\\\\page.jsx\",\n        lineNumber: 828,\n        columnNumber: 5\n    }, this);\n}\n_s(ConfigureVPSPage, \"5wTYs8D4HXndR5VBB5HKmg6p86Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations,\n        _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c4 = ConfigureVPSPage;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"UbuntuIcon\");\n$RefreshReg$(_c1, \"CentOSIcon\");\n$RefreshReg$(_c2, \"DebianIcon\");\n$RefreshReg$(_c3, \"WindowsIcon\");\n$RefreshReg$(_c4, \"ConfigureVPSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/configure/page.jsx\n"));

/***/ })

});