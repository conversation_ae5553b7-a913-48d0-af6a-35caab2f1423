const Package = require("../models/Package");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

// Cart Schema
const cartSchema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    items: [
      {
        package: {
          type: Schema.Types.ObjectId,
          ref: "Package",
          required: true,
        },
        quantity: {
          type: Number,
          required: true,
          min: 1,
        },
        price: {
          type: Number,
          required: true,
        },
        discount: {
          type: Number,
          required: true,
          default: 0,
        },
        period: {
          type: Number,
          required: true,
          default: 1,
        },
        // Custom configuration for VPS and other configurable services
        customConfiguration: {
          type: Schema.Types.Mixed,
          default: null,
        },
      },
    ],
    totalPrice: {
      type: Number,
      required: true,
      default: 0,
    },
    totalDiscount: {
      type: Number,
      required: true,
      default: 0,
    },
    cartCount: {
      type: Number,
      required: true,
      default: 0,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Pre-save hook to update total price and total discount when the cart items change
cartSchema.pre("save", function (next) {
  this.totalPrice = this.items.reduce(
    (acc, item) =>
      acc + item.price * item.quantity * item.period - item.discount,
    0
  );
  this.totalDiscount = this.items.reduce((acc, item) => acc + item.discount, 0);
  this.cartCount = this.items.reduce((acc, item) => acc + item.quantity, 0);
  this.updatedAt = Date.now();
  next();
});

// Method to add item to the cart
cartSchema.methods.addItem = async function (
  package,
  quantity = 1,
  period,
  customConfiguration = null
) {
  // discount
  // price,
  try {
    if (!package) {
      throw { message: "Package not found", code: "PACKAGE_NOT_FOUND" };
    }

    const existingItemIndex = this.items.findIndex(
      (item) => item.package?._id.toString() === package._id.toString()
    );

    if (existingItemIndex > -1) {
      // Item already in cart, update quantity
      if (this.items[existingItemIndex].quantity + quantity > 10) {
        throw {
          message: "errors.max_quantity_error",
          code: "MAX_QUANTITY_REACHED",
        };
      }

      this.items[existingItemIndex].quantity += quantity;
      this.items[existingItemIndex].price = package.price; // Update price

      // Find the correct discount based on the period
      console.log("period: ", this.items[existingItemIndex].period);
      const discountEntry = package.discounts.find(
        (d) => d.period === this.items[existingItemIndex].period && d.period > 0
      );
      console.log("discountEntryeee: ", discountEntry?.percentage);

      const discountPercentage = discountEntry ? discountEntry.percentage : 0;

      this.items[existingItemIndex].discount = this.calculateItemDiscount(
        this.items[existingItemIndex],
        package,
        discountPercentage
      );
    } else {
      // Item not in cart, add new item
      console.log(
        "Adding new item to cart with customConfiguration:",
        customConfiguration
      );
      this.items.push({
        package: package._id,
        quantity,
        price: package.price,
        period,
        customConfiguration,
      });

      const newItemIndex = this.items.length - 1;
      this.items[newItemIndex].price = package.price;

      // Find the correct discount based on the period
      const discountEntry = package.discounts.find(
        (d) => d.period === period && d.period > 0
      );
      console.log("discountEntryeee  new: ", discountEntry?.percentage);
      const discountPercentage = discountEntry ? discountEntry.percentage : 0;

      this.items[newItemIndex].discount = this.calculateItemDiscount(
        this.items[newItemIndex],
        package,
        discountPercentage
      );
    }

    return this.save();
  } catch (error) {
    throw error;
  }
};

cartSchema.methods.removeItem = async function (packageId, quantity = 1) {
  const package = await Package.findById(packageId);
  const existingItemIndex = this.items.findIndex(
    (item) => item.package?._id.toString() === packageId.toString()
  );

  if (existingItemIndex > -1) {
    // Item found in cart
    this.items[existingItemIndex].quantity -= quantity;
    this.items[existingItemIndex].price = package.price;

    const discountEntry = package.discounts.find(
      (d) => d.period === this.items[existingItemIndex].period && d.period > 0
    );
    const discountPercentage = discountEntry ? discountEntry.percentage : 0;

    this.items[existingItemIndex].discount = this.calculateItemDiscount(
      this.items[existingItemIndex],
      package,
      discountPercentage
    ); // Recalculate discount

    if (this.items[existingItemIndex].quantity <= 0) {
      // Remove item if quantity becomes zero or less
      this.items.splice(existingItemIndex, 1);
    }
  }
  return this.save();
};

// Method to clear the cart
cartSchema.methods.clearCart = function () {
  this.items = [];
  this.totalPrice = 0;
  return this.save();
};

cartSchema.methods.calculateItemDiscount = function (
  item,
  package,
  discountRate
) {
  return (discountRate / 100) * (package.price * item.quantity * item.period);
};

const Cart = mongoose.model("Cart", cartSchema);

module.exports = Cart;
